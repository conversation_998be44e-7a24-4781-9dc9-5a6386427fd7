import { apiClient } from './api'
import type { Store as AuthStore } from './auth-api'

// API response store structure (from the stores API)
export interface ApiStore {
  id: string
  active: number
  address: string
  city_uid: string
  created_at: number
  expiry_date: number
  email: string
  fb_store_id: number
  is_fabi: number
  updated_by: string
  updated_at: number
  brand_uid: string
  company_uid: string
  latitude: number
  longitude: number
  phone: string
  store_id: string
  store_name: string
  sort: number
  is_ahamove_active: number
  phone_manager: string
  delivery_services: string
  extra_data: Record<string, unknown>
  email_delivery_service: string
  city_name: string
  city_id: string
}

// Use the auth Store interface for consistency
export type Store = AuthStore

export interface StoreListParams {
  company_uid: string
  brand_uid: string
  page?: number
  limit?: number
  id?: string
}

export interface StoreListResponse {
  data: ApiStore[]
  total?: number
  page?: number
  limit?: number
}

/**
 * Get stores list from API
 */
export const getStores = async (params: StoreListParams): Promise<StoreListResponse> => {
  const response = await apiClient.get('/mdata/v1/stores', {
    params: {
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      page: params.page || 1,
      limit: params.limit || 50
    }
  })

  return response.data
}

/**
 * Get store by ID
 */
export const getStoreById = async (storeId: string): Promise<ApiStore> => {
  const response = await apiClient.get(`/mdata/v1/stores/${storeId}`)
  return response.data
}

/**
 * Create new store
 */
export const createStore = async (storeData: Partial<ApiStore>): Promise<ApiStore> => {
  const response = await apiClient.post('/mdata/v1/stores', storeData)
  return response.data
}

/**
 * Update store
 */
export const updateStore = async (
  storeId: string,
  storeData: Partial<ApiStore>
): Promise<ApiStore> => {
  const response = await apiClient.put(`/mdata/v1/stores/${storeId}`, storeData)
  return response.data
}

/**
 * Delete store
 */
export const deleteStore = async (storeId: string): Promise<void> => {
  await apiClient.delete(`/mdata/v1/stores/${storeId}`)
}

/**
 * Convert API store to Auth store format
 */
export const convertApiStoreToAuthStore = (apiStore: ApiStore): Store => {
  return {
    id: apiStore.id,
    brand_uid: apiStore.brand_uid,
    city_uid: apiStore.city_uid,
    company_uid: apiStore.company_uid,
    store_id: apiStore.store_id,
    store_name: apiStore.store_name,
    logo: '',
    background: '',
    facebook: '',
    website: '',
    fb_store_id: apiStore.fb_store_id,
    phone: apiStore.phone,
    address: apiStore.address,
    latitude: apiStore.latitude,
    active: apiStore.active,
    open_at: (apiStore.extra_data as Record<string, number>)?.open_at || 0,
    expiry_date: apiStore.expiry_date,
    sort: apiStore.sort,
    enable_change_item_in_store:
      (apiStore.extra_data as Record<string, number>)?.enable_change_item_in_store || 0,
    enable_change_item_type_in_store:
      (apiStore.extra_data as Record<string, number>)?.enable_change_item_type_in_store || 0,
    enable_change_printer_position_in_store:
      (apiStore.extra_data as Record<string, number>)?.enable_change_printer_position_in_store || 0,
    enable_turn_order_report:
      (apiStore.extra_data as Record<string, number>)?.enable_turn_order_report || 0,
    sale_change_vat_enable:
      (apiStore.extra_data as Record<string, number>)?.sale_change_vat_enable || 0,
    bill_template: (apiStore.extra_data as Record<string, number>)?.bill_template || 0,
    is_franchise: (apiStore.extra_data as Record<string, number>)?.is_franchise || 0,
    tracking_sale: (apiStore.extra_data as Record<string, number>)?.tracking_sale || 0,
    dateend: new Date(apiStore.expiry_date * 1000).toISOString(),
    istrial: (apiStore.extra_data as Record<string, Record<string, boolean>>)?.subscription_info
      ?.isTrial
      ? '1'
      : '0',
    change_log_detail: 0
  }
}

/**
 * Get stores from localStorage (for offline/cached data)
 * Returns stores from the login response
 */
export const getStoredStores = (): Store[] => {
  try {
    const stored = localStorage.getItem('pos_stores')
    return stored ? JSON.parse(stored) : []
  } catch (_error) {
    // Silent error handling - return empty array if parsing fails
    return []
  }
}

/**
 * Get API stores from localStorage (detailed store data from stores API)
 */
export const getStoredApiStores = (): ApiStore[] => {
  try {
    const stored = localStorage.getItem('pos_stores_data')
    return stored ? JSON.parse(stored) : []
  } catch (_error) {
    // Silent error handling - return empty array if parsing fails
    return []
  }
}

/**
 * Save API stores to localStorage
 */
export const saveApiStoresToStorage = (stores: ApiStore[]): void => {
  try {
    localStorage.setItem('pos_stores_data', JSON.stringify(stores))
  } catch (_error) {
    // Silent error handling - localStorage might be full or unavailable
  }
}

/**
 * Get API stores filtered by brand from localStorage
 */
export const getStoredApiStoresByBrand = (brandId: string): ApiStore[] => {
  const stores = getStoredApiStores()
  return stores.filter(store => store.brand_uid === brandId)
}

/**
 * Get active API stores from localStorage
 */
export const getActiveStoredApiStores = (): ApiStore[] => {
  const stores = getStoredApiStores()
  return stores.filter(store => store.active === 1)
}

/**
 * Get active API stores by brand from localStorage
 */
export const getActiveStoredApiStoresByBrand = (brandId: string): ApiStore[] => {
  const stores = getStoredApiStores()
  return stores.filter(store => store.brand_uid === brandId && store.active === 1)
}

/**
 * Search API stores by name from localStorage
 */
export const searchStoredApiStores = (query: string): ApiStore[] => {
  const stores = getStoredApiStores()
  const searchTerm = query.toLowerCase()

  return stores.filter(
    store =>
      store.store_name.toLowerCase().includes(searchTerm) ||
      store.store_id.toLowerCase().includes(searchTerm) ||
      store.address.toLowerCase().includes(searchTerm) ||
      store.phone.includes(searchTerm)
  )
}

/**
 * Get stores filtered by brand from localStorage (Auth format)
 */
export const getStoredStoresByBrand = (brandId: string): Store[] => {
  const stores = getStoredStores()
  return stores.filter(store => store.brand_uid === brandId)
}

/**
 * Get active stores from localStorage (Auth format)
 */
export const getActiveStoredStores = (): Store[] => {
  const stores = getStoredStores()
  return stores.filter(store => store.active === 1)
}

/**
 * Get active stores by brand from localStorage (Auth format)
 */
export const getActiveStoredStoresByBrand = (brandId: string): Store[] => {
  const stores = getStoredStores()
  return stores.filter(store => store.brand_uid === brandId && store.active === 1)
}

/**
 * Search stores by name from localStorage (Auth format)
 */
export const searchStoredStores = (query: string): Store[] => {
  const stores = getStoredStores()
  const searchTerm = query.toLowerCase()

  return stores.filter(
    store =>
      store.store_name.toLowerCase().includes(searchTerm) ||
      store.store_id.toLowerCase().includes(searchTerm) ||
      store.address.toLowerCase().includes(searchTerm) ||
      store.phone.includes(searchTerm)
  )
}

/**
 * Get store statistics from localStorage
 */
export const getStoreStats = () => {
  const stores = getStoredApiStores()
  const activeStores = stores.filter(store => store.active === 1)

  return {
    total: stores.length,
    active: activeStores.length,
    inactive: stores.length - activeStores.length,
    cities: [...new Set(stores.map(store => store.city_name))].length
  }
}

// Request deduplication - track ongoing requests
const ongoingRequests = new Map<string, Promise<ApiStore[]>>()

// Cache for recent requests to prevent unnecessary API calls
const requestCache = new Map<string, { data: ApiStore[]; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

/**
 * Fetch stores from API and save to localStorage
 * Includes request deduplication to prevent multiple simultaneous requests
 */
export const fetchAndSyncStores = async (
  companyUid: string,
  brandUid: string
): Promise<ApiStore[]> => {
  const requestKey = `${companyUid}-${brandUid}`

  // Check cache first
  const cached = requestCache.get(requestKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }

  // If there's already an ongoing request for this company/brand, return it
  if (ongoingRequests.has(requestKey)) {
    return ongoingRequests.get(requestKey)!
  }

  const requestPromise = (async () => {
    try {
      const response = await getStores({
        company_uid: companyUid,
        brand_uid: brandUid,
        page: 1,
        limit: 100 // Get all stores
      })

      if (response.data) {
        saveApiStoresToStorage(response.data)
        // Cache the result
        requestCache.set(requestKey, {
          data: response.data,
          timestamp: Date.now()
        })
        return response.data
      }

      return []
    } catch (_error) {
      // Silent error handling - return cached data if API fails
      return getStoredApiStores()
    } finally {
      // Clean up the ongoing request
      ongoingRequests.delete(requestKey)
    }
  })()

  // Store the promise to prevent duplicate requests
  ongoingRequests.set(requestKey, requestPromise)

  return requestPromise
}
