import type { ItemCategory, ItemCategoryApiData } from '@/types/item-categories'
import { convertApiItemCategoryToItemCategory } from '@/types/item-categories'
import * as XLSX from 'xlsx'

import { apiClient } from './api'

// API response interface
interface ItemCategoriesApiResponse {
  data: ItemCategoryApiData[]
  track_id: string
}

// Parameters for fetching item categories
export interface GetItemCategoriesParams {
  searchTerm?: string
  page?: number
}

// Create item category parameters
export interface CreateItemCategoryParams {
  name: string
  code?: string
  sort?: number
  selectedItems?: string[]
  parentId?: string | null
  store_uid?: string
}

// Created item category response
export interface CreatedItemCategory {
  active: number
  revision: number
  created_at: number
  updated_at: number
  deleted: boolean
  item_type_name: string
  company_uid: string
  brand_uid: string
  item_type_id: string
  sort: number
  is_fabi: number
  item_type_parent_id: string
  item_type_color: string
  list_order: number
  is_material: number
  sort_online: number
  print_name_menu: string
  image_path: string
  description: string
  extra_data: Record<string, any>
  store_uid: string | null
  created_by: string
  updated_by: string | null
  deleted_by: string | null
  deleted_at: string | null
}

// API response wrapper
export interface CreateItemCategoryResponse {
  data: CreatedItemCategory
  track_id: string
}

// Bulk create item categories parameters
export interface BulkCreateItemCategoryParams {
  item_type_name: string
  item_type_id: string
  sort?: number
}

// Update item category parameters
export interface UpdateItemCategoryParams {
  name?: string
  parentId?: string | null
  store_uid?: string
}

/**
 * Fetch item categories from the API
 */
export const getItemCategories = async (
  params: GetItemCategoriesParams = {}
): Promise<ItemCategory[]> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    page: (params.page || 1).toString()
  })

  // Add search term if provided
  if (params.searchTerm) {
    queryParams.append('search', params.searchTerm)
  }

  const apiUrl = `/mdata/v1/item-types?${queryParams.toString()}`

  const response = await apiClient.get<ItemCategoriesApiResponse>(apiUrl)

  if (response.data?.data) {
    // Convert API data to ItemCategory objects
    const convertedCategories = response.data.data.map((apiItemCategory: ItemCategoryApiData) => {
      return convertApiItemCategoryToItemCategory(apiItemCategory)
    })

    return convertedCategories
  }
  return []
}

// Created item category response

/**
 * Create a new item category
 */
export const createItemCategory = async (
  params: CreateItemCategoryParams
): Promise<CreatedItemCategory> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Generate item_type_id if not provided
  const generateItemTypeId = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return `ITEM_TYPE-${result}`
  }

  // Build payload matching the cURL command structure
  const payload = {
    item_type_name: params.name,
    company_uid: companyUid,
    brand_uid: brandUid,
    item_type_id: params.code && params.code.trim() ? params.code : generateItemTypeId(),
    ...(params.sort !== undefined && { sort: params.sort }),
    ...(params.selectedItems &&
      params.selectedItems.length > 0 && { list_item: params.selectedItems }),
    ...(params.parentId && { item_type_parent_id: params.parentId }),
    ...(params.store_uid && { store_uid: params.store_uid })
  }

  const apiUrl = `/mdata/v1/item-type`

  const response = await apiClient.post<CreateItemCategoryResponse>(apiUrl, payload)
  return response.data.data
}

/**
 * Bulk create item categories
 */
export const bulkCreateItemCategories = async (
  categories: BulkCreateItemCategoryParams[]
): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build payload array
  const payload = categories.map((category, index) => ({
    company_uid: companyUid,
    brand_uid: brandUid,
    item_type_name: category.item_type_name,
    item_type_id: category.item_type_id,
    sort: category.sort || index + 1
  }))

  const apiUrl = `/mdata/v1/item-types`

  await apiClient.post(apiUrl, payload)
}

/**
 * Update an existing item category
 */
export const updateItemCategory = async (
  categoryId: string,
  params: UpdateItemCategoryParams
): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build payload
  const payload = {
    company_uid: companyUid,
    brand_uid: brandUid,
    ...(params.name && { item_type_name: params.name }),
    ...(params.parentId && { item_type_parent_id: params.parentId }),
    ...(params.store_uid && { store_uid: params.store_uid })
  }

  const apiUrl = `/mdata/v1/item-types/${categoryId}`

  await apiClient.put(apiUrl, payload)
}

/**
 * Update item category status (activate/deactivate)
 */
export const updateItemCategoryStatus = async (category: ItemCategory): Promise<void> => {
  // Create the payload with all category data
  const payload = {
    ...category,
    list_item: [] // Add empty array as shown in the curl example
  }

  const apiUrl = `/mdata/v1/item-type`

  await apiClient.put(apiUrl, payload)
}

/**
 * Delete an item category
 */
export const deleteItemCategory = async (categoryId: string): Promise<void> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters for DELETE request
  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: categoryId
  })

  const apiUrl = `/mdata/v1/item-type?${queryParams.toString()}`

  await apiClient.delete(apiUrl)
}

/**
 * Import item categories from a file
 */
export const importCategories = async (file: File): Promise<void> => {
  const formData = new FormData()
  formData.append('file', file)

  const apiUrl = `/mdata/v1/item-types/import`

  await apiClient.post(apiUrl, formData)
}

/**
 * Get a single item category by ID (based on item-class API pattern)
 */
export const getItemCategoryById = async (id: string): Promise<ItemCategory> => {
  const response = await apiClient.get<{ data: ItemCategoryApiData; track_id: string }>(
    '/mdata/v1/item-type',
    {
      params: {
        id
      }
    }
  )

  return convertApiItemCategoryToItemCategory(response.data.data)
}

export interface ExportCategoriesParams {
  store_uid?: string
  apply_with_store?: boolean
}

/**
 * Export item categories
 */
export const exportCategories = async (params: ExportCategoriesParams = {}): Promise<Blob> => {
  const posUserData = localStorage.getItem('pos_user_data')
  const posBrandData = localStorage.getItem('pos_brands_data')
  let companyUid = ''
  let brandUid = ''

  if (posUserData) {
    try {
      const userData = JSON.parse(posUserData)
      companyUid = userData.company_uid || ''
    } catch {
      // Error parsing user data
    }
  }

  if (posBrandData) {
    try {
      const brandData = JSON.parse(posBrandData)
      // pos_brands_data is an array, get the first brand
      if (Array.isArray(brandData) && brandData.length > 0) {
        brandUid = brandData[0].id || ''
      }
    } catch {
      // Error parsing brand data
    }
  }

  if (!companyUid || !brandUid) {
    throw new Error('Company or brand UID not found in localStorage')
  }

  // Build query parameters for export request
  const queryParams = new URLSearchParams({
    skip_limit: 'true',
    company_uid: companyUid,
    brand_uid: brandUid
  })

  // Add store-specific parameters if provided
  if (params.store_uid) {
    queryParams.append('store_uid', params.store_uid)
  }

  if (params.apply_with_store) {
    queryParams.append('apply_with_store', '1')
  }

  const apiUrl = `/mdata/v1/item-types?${queryParams.toString()}`

  const response = await apiClient.get<ItemCategoriesApiResponse>(apiUrl)

  if (response.data?.data) {
    // Convert API data to ItemCategory objects
    const categories = response.data.data.map((apiItemCategory: ItemCategoryApiData) => {
      return convertApiItemCategoryToItemCategory(apiItemCategory)
    })

    // Create XLSX export and return as Blob
    return createCategoriesExcelBlob(categories)
  } else {
    throw new Error('No categories data received from API')
  }
}

/**
 * Create Excel file for categories export with specific format
 */
const createCategoriesExcelBlob = (categories: ItemCategory[]): Blob => {
  // Prepare data for Excel with the specific format requested
  const excelData = [
    // Row 1: Title
    ['Báo cáo nhóm món'],
    // Row 2: Headers
    ['#', 'Mã nhóm', 'Tên nhóm', 'Thứ tự', 'Trạng thái'],
    // Data rows starting from Row 3
    ...categories.map((category, index) => [
      index + 1, // #
      category.item_type_id, // Mã nhóm
      category.item_type_name, // Tên nhóm
      category.sort || 0, // Thứ tự
      category.active === 1 ? 'Active' : 'Inactive' // Trạng thái
    ])
  ]

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.aoa_to_sheet(excelData)

  // Set column widths
  const columnWidths = [
    { wch: 5 }, // #
    { wch: 20 }, // Mã nhóm
    { wch: 30 }, // Tên nhóm
    { wch: 10 }, // Thứ tự
    { wch: 15 } // Trạng thái
  ]
  worksheet['!cols'] = columnWidths

  // Merge cells for title (Row 1)
  worksheet['!merges'] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 4 } } // Merge A1:E1 for title
  ]

  // Style the title cell (make it bold and centered)
  if (worksheet['A1']) {
    worksheet['A1'].s = {
      font: { bold: true, sz: 14 },
      alignment: { horizontal: 'center', vertical: 'center' }
    }
  }

  // Style header row (Row 2)
  const headerCells = ['A2', 'B2', 'C2', 'D2', 'E2']
  headerCells.forEach(cellRef => {
    if (worksheet[cellRef]) {
      worksheet[cellRef].s = {
        font: { bold: true },
        alignment: { horizontal: 'center', vertical: 'center' },
        fill: { fgColor: { rgb: 'E6E6E6' } }
      }
    }
  })

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Báo cáo nhóm món')

  // Write workbook to binary string
  const workbookBinary = XLSX.write(workbook, {
    bookType: 'xlsx',
    type: 'binary'
  })

  // Convert binary string to ArrayBuffer
  const buffer = new ArrayBuffer(workbookBinary.length)
  const view = new Uint8Array(buffer)
  for (let i = 0; i < workbookBinary.length; ++i) {
    view[i] = workbookBinary.charCodeAt(i) & 0xff
  }

  // Create and return the Blob
  return new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })
}

// Export API object
export const itemCategoriesApi = {
  getItemCategories,
  getItemCategoryById,
  createItemCategory,
  bulkCreateItemCategories,
  updateItemCategory,
  updateItemCategoryStatus,
  deleteItemCategory,
  importCategories,
  exportCategories
}
