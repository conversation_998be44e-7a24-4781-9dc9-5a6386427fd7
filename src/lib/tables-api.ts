import { apiClient } from './api'

export interface Table {
  id: string
  table_id: string
  table_name: string
  description?: string
  extra_data?: {
    order_list?: any[]
  }
  active: number
  revision?: number
  sort: number
  is_fabi: number
  source_id?: string
  area_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  area_id: string
  store_id?: string
  brand_id?: string
  company_id?: string
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  area: {
    id: string
    area_id: string
    area_name: string
    active: number
    sort: number
    extra_data?: any
  }
}

export interface TablesListParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  page?: number
  limit?: number
}

export interface TablesListResponse {
  data: Table[]
  track_id: string
}

export interface Area {
  id: string
  area_id: string
  area_name: string
  description?: string
  extra_data?: any
  active: number
  revision: number
  sort: number
  store_uid: string
  brand_uid: string
  company_uid: string
  store_id?: string
  brand_id?: string
  company_id?: string
  is_fabi: number
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  list_table_id: string[]
}

export interface AreasListParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  skip_limit?: boolean
  page?: number
  results_per_page?: number
}

export interface AreasListResponse {
  data: Area[]
  track_id: string
}

export interface DeleteTableRequest {
  company_uid: string
  brand_uid: string
  store_uid: string
  list_id: string[]
}

export interface UpdateTableStatusRequest {
  id: string
  table_id: string
  table_name: string
  description: string | null
  extra_data: {
    color: string
    font_size: string
    order_list: Array<{
      item_id: string
      quantity: number
    }>
  }
  active: number
  revision: string | null
  sort: number
  is_fabi: number
  source_id: string | null
  area_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  area_id: string
  store_id: string | null
  brand_id: string | null
  company_id: string | null
  created_by: string
  updated_by: string
  created_at: string
  updated_at: string
  area: {
    id: string
    area_id: string
    area_name: string
    active: number
    sort: number
    extra_data: {
      background: string
    }
  }
}

export interface CreateTableRequest {
  area_name: string
  area_id?: string
  description?: string
  store_uid: string
  brand_uid: string
  company_uid: string
  active?: number
  sort?: number
  extra_data?: {
    background?: string
  }
}

export interface UpdateTableRequest extends CreateTableRequest {
  id: string
}

export interface DeleteTablesRequest {
  company_uid: string
  brand_uid: string
  list_id: string[]
  store_uid: string
  area_uid?: string
}

export interface BulkImportTableItem {
  company_uid: string
  brand_uid: string
  area_name: string
  area_id: string
  description?: string
  store_uid: string
  sort: number
}

export type BulkImportTablesRequest = BulkImportTableItem[]

export interface ImageUploadResponse {
  data: {
    image_url: string
  }
  track_id: string
}

/**
 * Tables API client
 */
export const tablesApi = {
  /**
   * Get tables list
   */
  getTablesList: async (params: TablesListParams): Promise<TablesListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      store_uid: params.store_uid,
      page: (params.page || 1).toString(),
      ...(params.limit && { limit: params.limit.toString() })
    })

    const response = await apiClient.get(`/pos/v1/table?${queryParams.toString()}`)
    return response.data
  },

  /**
   * Update an existing table
   */
  updateTable: async (data: Table): Promise<Table> => {
    // Use POST method to /pos/v1/area with complete table object (like in cURL)
    console.log('Updating table with POST:', data)
    const response = await apiClient.post('/pos/v1/area', data)
    return response.data.data || response.data
  },

  /**
   * Get table by ID
   */
  getTableById: async (
    id: string,
    companyUid: string,
    brandUid: string,
    storeUid: string
  ): Promise<Table> => {
    const queryParams = new URLSearchParams({
      company_uid: companyUid,
      brand_uid: brandUid,
      store_uid: storeUid,
      id: id
    })

    const response = await apiClient.get(`/pos/v1/area?${queryParams.toString()}`)
    return response.data.data || response.data
  },

  /**
   * Delete multiple tables
   */
  deleteTables: async (params: DeleteTablesRequest): Promise<void> => {
    const response = await apiClient.delete('/pos/v1/table', {
      data: params
    })
    return response.data
  },

  /**
   * Upload image for table background
   */
  uploadImage: async (file: File): Promise<ImageUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await apiClient.post('/v3/pos-cms/image/upload', formData, {
      headers: {
        // Let axios set Content-Type automatically with boundary
        'Content-Type': undefined
      }
    })

    return response.data
  },

  /**
   * Create a new table
   */
  createTable: async (data: CreateTableRequest): Promise<Table> => {
    const response = await apiClient.post('/pos/v1/area', [data])
    return response.data.data?.[0] || response.data[0]
  },

  /**
   * Bulk import tables
   */
  bulkImportTables: async (tables: BulkImportTablesRequest): Promise<Table[]> => {
    const response = await apiClient.post('/pos/v1/area', tables)
    return response.data.data || response.data
  },

  /**
   * Get areas list for filtering
   */
  getAreasList: async (params: AreasListParams): Promise<AreasListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      store_uid: params.store_uid,
      skip_limit: 'true',
      page: (params.page || 1).toString(),
      results_per_page: (params.results_per_page || 15000).toString()
    })

    const response = await apiClient.get(`/pos/v1/area?${queryParams.toString()}`)
    return response.data
  },

  /**
   * Delete table
   */
  deleteTable: async (params: DeleteTableRequest): Promise<void> => {
    const response = await apiClient.delete('/pos/v1/table', {
      data: params
    })
    return response.data
  },

  /**
   * Update table status (activate/deactivate)
   */
  updateTableStatus: async (tableData: UpdateTableStatusRequest): Promise<Table> => {
    // Use the same endpoint as the cURL - POST to /pos/v1/table with full table data
    const response = await apiClient.post('/pos/v1/table', tableData)
    return response.data.data || response.data
  }
}

export default tablesApi
