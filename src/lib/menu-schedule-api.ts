import type { MenuSchedule } from '@/features/menu/schedule/data'
import type {
  UpdateItemSchedulePayload,
  MenuScheduleApiResponse,
  MenuItem
} from '@/features/menu/schedule/types/menu-schedule-api'

/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from './api'

export interface MenuScheduleListParams {
  company_uid?: string
  brand_uid?: string
  city_uid?: string
  store_uid?: string
  active?: string
  page?: number
  limit?: number
  [key: string]: string | number | undefined // Allow flexible additional params
}

export interface MenuScheduleListResponse {
  data: MenuSchedule[]
  total?: number
  page?: number
  limit?: number
}

export interface MenuScheduleItemData {
  id?: string
  unit_uid?: string
  ta_price?: number
  ots_tax?: number
  ta_tax?: number
  time_sale_hour_day?: number
  time_sale_date_week?: number
  allow_take_away?: number
  is_eat_with?: number
  image_path?: string
  image_path_thumb?: string | null
  item_color?: string
  list_order?: number
  is_service?: number
  is_material?: number
  active?: number
  user_id?: string
  is_foreign?: number
  quantity_default?: number
  price_change?: number
  currency_type_id?: string
  point?: number
  is_gift?: number
  is_fc?: number
  show_on_web?: number
  show_price_on_web?: number
  cost_price?: number
  is_print_label?: number
  quantity_limit?: number
  is_kit?: number
  time_cooking?: number
  item_id_barcode?: string
  process_index?: number
  is_allow_discount?: number
  quantity_per_day?: number
  item_id_eat_with?: string
  is_parent?: number
  is_sub?: number
  item_id_mapping?: string
  effective_date?: number
  expire_date?: number
  extra_data?: {
    price_by_source?: unknown[]
    is_virtual_item?: number
    is_item_service?: number
    no_update_quantity_toping?: number
    enable_edit_price?: number
    is_buffet_item?: number
    exclude_items_buffet?: unknown[]
    up_size_buffet?: unknown[]
  }
  revision?: number
  source_uid?: string | null
  deleted?: boolean
  created_by?: string
  updated_by?: string
  deleted_by?: string | null
  created_at?: number
  updated_at?: number
  deleted_at?: number | null
  is_fabi?: number
  city_uid?: string
  store_uid?: string
  item_type_uid?: string
  item_name?: string
  company_uid?: string
  brand_uid?: string
  item_id?: string
  ots_price?: number
  customization_uid?: string | null
  item_class_uid?: string | null
  unit_secondary_uid?: string | null
  sort?: number
  apply_with_store?: number
  description?: string
}

export interface CreateMenuSchedulePayload {
  company_uid: string
  brand_uid: string
  type: 'item'
  action: 'CREATE' | 'UPDATE' | 'DELETE'
  item_id: string
  changed_data: MenuScheduleItemData
  store_uid: string
  city_uid: string
  time: number
  end_time?: number | null
  item_uid?: string
}

/**
 * Get menu schedules list from API
 */
export const getMenuSchedules = async (
  params: MenuScheduleListParams = {}
): Promise<MenuSchedule[]> => {
  const queryParams: Record<string, string | number> = {}

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams[key] = value
    }
  })

  if (!queryParams.page) {
    queryParams.page = 1
  }

  const response = await apiClient.get('/mdata/v1/item-schedules', {
    params: queryParams
  })

  return response.data?.data || response.data || []
}

/**
 * Get menu schedule by specific parameters (for editing)
 */
export const getMenuScheduleByParams = async (params: {
  company_uid: string
  brand_uid: string
  time: number
  store_uid: string
  city_uid: string
}): Promise<{
  data: {
    item_schedules: Array<{
      id: string
      item_id: string
      type: string
      action: string
      time: number
      end_time: number | null
      changed_data: Record<string, unknown>
      data_old: Record<string, unknown>
      status: string
      store_uid: string
      city_uid: string
      brand_uid: string
      company_uid: string
    }>
    company_uid: string
    brand_uid: string
    store_uid: string
    city_uid: string
    time: number
    end_time: number | null
    status: string
  }
}> => {
  const response = await apiClient.get('/mdata/v1/item-schedule', {
    params
  })
  return response.data
}

/**
 * Get menu schedule by ID
 */
export const getMenuScheduleById = async (id: string): Promise<MenuSchedule> => {
  const response = await apiClient.get(`/mdata/v1/item-schedules/${id}`)
  return response.data
}

/**
 * Create new menu schedule
 */
export const createMenuSchedule = async (
  scheduleItems: CreateMenuSchedulePayload[]
): Promise<unknown> => {
  const response = await apiClient.post('/mdata/v1/item-schedule', scheduleItems)
  return response.data
}

/**
 * Helper function to create menu schedule payload from form data
 */
export const createMenuSchedulePayload = (
  formData: {
    company_uid: string
    brand_uid: string
    city_uid: string
    store_uid: string
    start_date: Date
    end_date?: Date | null
  },
  menuItems: MenuItem[]
): CreateMenuSchedulePayload[] => {
  return menuItems.map(item => {
    const basePayload = {
      company_uid: formData.company_uid,
      brand_uid: formData.brand_uid,
      type: 'item' as const,
      action: item.action,
      item_id: item.code,
      store_uid: formData.store_uid,
      city_uid: formData.city_uid,
      time: formData.start_date.getTime(),
      end_time: formData.end_date?.getTime() || null
    }

    if (item.action === 'UPDATE' && item.originalItem) {
      return {
        ...basePayload,
        item_uid: item.originalItem.id as string,
        changed_data: {
          ...item.originalItem,
          item_name: item.name,
          ots_price: (item.ots_price || item.originalItem.ots_price) as number,
          ots_tax: (item.vat_rate || item.originalItem.ots_tax) as number,
          time_cooking: (item.cooking_time || item.originalItem.time_cooking) as number,
          unit_uid: (item.unit_uid || item.originalItem.unit_uid) as string,
          unit_secondary_uid: (item.unit_secondary_uid ||
            item.originalItem.unit_secondary_uid) as string,
          item_type_uid: (item.item_type_uid || item.originalItem.item_type_uid) as string,
          item_class_uid: (item.item_class_uid || item.originalItem.item_class_uid) as string,
          customization_uid: (item.customization_uid ||
            item.originalItem.customization_uid) as string,
          sort: (item.display_order || item.originalItem.sort) as number,
          extra_data: {
            ...(item.originalItem.extra_data as any),
            enable_edit_price: item.allow_edit_price
              ? 1
              : (item.originalItem.extra_data as any)?.enable_edit_price || 0,
            no_update_quantity_toping: item.require_quantity_input
              ? 0
              : (item.originalItem.extra_data as any)?.no_update_quantity_toping || 1,
            is_buffet_item: item.is_buffet
              ? 1
              : (item.originalItem.extra_data as any)?.is_buffet_item || 0,
            is_item_service: item.is_service
              ? 1
              : (item.originalItem.extra_data as any)?.is_item_service || 0
          },
          apply_with_store: 1,
          store_uid: formData.store_uid,
          city_uid: formData.city_uid
        }
      }
    }

    return {
      ...basePayload,
      changed_data: {
        unit_uid: item.unit_uid || '',
        ta_price: 0,
        ots_tax: item.vat_rate || 0,
        ta_tax: 0,
        extra_data: {
          price_by_source: [],
          is_virtual_item: 0,
          is_item_service: item.is_service ? 1 : 0,
          no_update_quantity_toping: item.require_quantity_input ? 0 : 1,
          enable_edit_price: item.allow_edit_price ? 1 : 0,
          is_buffet_item: item.is_buffet ? 1 : 0,
          exclude_items_buffet: [],
          up_size_buffet: []
        },
        city_uid: formData.city_uid,
        store_uid: formData.store_uid,
        item_type_uid: item.item_type_uid || '',
        item_name: item.name,
        company_uid: formData.company_uid,
        brand_uid: formData.brand_uid,
        item_id: item.code,
        ots_price: item.ots_price || 0,
        image_path_thumb: null,
        customization_uid: item.customization_uid || null,
        item_class_uid: item.item_class_uid || null,
        unit_secondary_uid: item.unit_secondary_uid || null,
        sort: item.display_order || 1000,
        apply_with_store: 2
      }
    }
  })
}

/**
 * Update menu schedule
 */
export const updateMenuSchedule = async (
  id: string,
  scheduleData: Partial<MenuSchedule>
): Promise<MenuSchedule> => {
  const response = await apiClient.put(`/mdata/v1/item-schedules/${id}`, scheduleData)
  return response.data
}

/**
 * Update item schedule with array payload (matches API format)
 */
export const updateItemSchedule = async (
  payload: UpdateItemSchedulePayload[]
): Promise<MenuScheduleApiResponse> => {
  const response = await apiClient.put('/mdata/v1/item-schedule', payload)
  return response.data
}

/**
 * Delete menu schedule
 */
export const deleteMenuSchedule = async (scheduleData: {
  time: number
  store_uid: string
  city_uid: string
  brand_uid: string
  company_uid: string
}): Promise<void> => {
  const queryParams: Record<string, string | number> = {
    time: scheduleData.time,
    store_uid: scheduleData.store_uid,
    city_uid: scheduleData.city_uid,
    brand_uid: scheduleData.brand_uid,
    company_uid: scheduleData.company_uid
  }

  await apiClient.delete('/mdata/v1/item-schedule', {
    params: queryParams
  })
}

/**
 * Delete item schedule with array payload (matches API format)
 */
export const deleteItemSchedule = async (
  payload: UpdateItemSchedulePayload[]
): Promise<MenuScheduleApiResponse> => {
  const response = await apiClient.put('/mdata/v1/item-schedule', payload)
  return response.data
}

/**
 * Get menu schedules by date range
 */
export const getMenuSchedulesByDateRange = async (
  startDate: string,
  endDate: string,
  params: Omit<MenuScheduleListParams, 'page' | 'limit'> = {}
): Promise<MenuSchedule[]> => {
  const queryParams: Record<string, string | number> = {
    start_date: startDate,
    end_date: endDate
  }

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams[key] = value
    }
  })

  const response = await apiClient.get('/mdata/v1/item-schedules', {
    params: queryParams
  })

  return response.data?.data || response.data || []
}

/**
 * Get active menu schedules for a specific store
 */
export const getActiveMenuSchedulesForStore = async (
  storeUid: string,
  date?: string
): Promise<MenuSchedule[]> => {
  const queryParams: Record<string, string | number> = {
    store_uid: storeUid
  }

  if (date) {
    queryParams.date = date
  }

  const response = await apiClient.get('/mdata/v1/item-schedules', {
    params: queryParams
  })

  return response.data?.data || response.data || []
}

// Export all functions as a single object for easier importing
export const menuScheduleApi = {
  getMenuSchedules,
  getMenuScheduleById,
  getMenuScheduleByParams,
  createMenuSchedule,
  createMenuSchedulePayload,
  updateMenuSchedule,
  updateItemSchedule,
  deleteMenuSchedule,
  deleteItemSchedule,
  getMenuSchedulesByDateRange,
  getActiveMenuSchedulesForStore
}
