import { apiClient } from './api'

export interface ComboPromotion {
  promotion_uid: string
  active: number
  created_at: number
  store_uid: string
  store_name: string
}

export interface ComboPromotionGroup {
  promotion_id: string
  promotion_name: string
  partner_auto_gen: number
  array_agg: number[]
  ids_same_promotion: string[]
  promotions: ComboPromotion[]
}

export interface ComboPromotionsListParams {
  company_uid: string
  brand_uid: string
  skip_limit?: boolean
  aggregate?: boolean
  store_uid?: string
}

export interface ComboPromotionsListResponse {
  data: ComboPromotionGroup[]
  track_id: string
}

export interface ComboPackageDetail {
  LstItem_Options: Array<{
    id: string
    Name: string
    LstItem: Array<{
      item_id: string
      ta_price: number
      item_name: string
      ots_price: number
      state_change_price: number
      discount_combo_item: number
    }>
    Max_Permitted: number
    Min_Permitted: number
  }>
}

export interface Combo {
  package_id: string
  package_name: string
  use_same_data: number
  ots_value: number
  from_date: number
  to_date: number
  id: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string | null
  deleted: boolean
  deleted_at: string | null
  deleted_by: string | null
  ta_value: number
  time_sale_hour_day: number | null
  time_sale_date_week: number | null
  description: string
  active: number
  extra_data: {
    price_by_source: any[]
  }
  package_detail: ComboPackageDetail
  revision: string | null
  unit_uid: string | null
  promotion_uid: string | null
  brand_uid: string
  company_uid: string
  sort: number
  image_path: string | null
  image_path_thumb: string | null
  package_color: string | null
  is_fabi: number
  store_uid: string
  vat_tax_rate: number
  item_type_uid: string | null
  ps_store_uid: string
  row: number
  stores: number
  list_package_uid: string[]
  list_store_uid: string[]
}

export interface CombosListParams {
  company_uid: string
  brand_uid: string
  page?: number
  list_store_uid?: string | string[]
  status?: 'unexpired' | 'expired' | 'all'
  search?: string
  promotion_id?: string
}

export interface CombosListResponse {
  data: Combo[]
  track_id: string
}

export interface DeleteCombosParams {
  packageUids: string[]
}

export interface GetComboDetailsParams {
  packageUids: string[]
}

export interface CreateCombosParams {
  combos: Omit<Combo, 'id' | 'created_at' | 'updated_at'>[]
}

/**
 * Combos API functions
 */
export const combosApi = {
  /**
   * Get combo promotions list
   */
  getComboPromotionsList: async (
    params: ComboPromotionsListParams
  ): Promise<ComboPromotionsListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      skip_limit: (params.skip_limit ?? true).toString(),
      aggregate: (params.aggregate ?? true).toString()
    })

    if (params.store_uid) {
      queryParams.append('store_uid', params.store_uid)
    }

    const response = await apiClient.get(`/mdata/v1/promotions?${queryParams}`)
    return response.data
  },

  /**
   * Get combos list
   */
  getCombosList: async (params: CombosListParams): Promise<CombosListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      page: (params.page || 1).toString()
    })

    if (params.list_store_uid) {
      const storeUids = Array.isArray(params.list_store_uid)
        ? params.list_store_uid.join(',')
        : params.list_store_uid
      queryParams.append('list_store_uid', storeUids)
    }

    if (params.status && params.status !== 'all') {
      queryParams.append('status', params.status)
    }

    if (params.search) {
      queryParams.append('search', params.search)
    }

    if (params.promotion_id) {
      queryParams.append('promotion_id', params.promotion_id)
    }

    const response = await apiClient.get(`/mdata/v1/packages?${queryParams}`)
    return response.data
  },

  /**
   * Delete combos
   */
  deleteCombos: async (params: DeleteCombosParams): Promise<void> => {
    const queryParams = new URLSearchParams({
      list_package_uid: params.packageUids.join(',')
    })

    await apiClient.delete(`/mdata/v1/packages?${queryParams}`)
  },

  /**
   * Get combo details for copying
   */
  getComboDetails: async (params: GetComboDetailsParams): Promise<{ data: Combo[] }> => {
    const queryParams = new URLSearchParams({
      list_package_uid: params.packageUids.join(',')
    })

    const response = await apiClient.get(`/mdata/v1/package?${queryParams}`)
    return response.data
  },

  /**
   * Create new combos (for copying)
   */
  createCombos: async (params: CreateCombosParams): Promise<void> => {
    await apiClient.post('/mdata/v1/packages', params.combos)
  }
}
