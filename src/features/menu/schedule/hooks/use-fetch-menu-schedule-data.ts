import { toast } from 'sonner'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

import type { MenuSchedule } from '../data'
import type { MenuItem, MenuScheduleWithItems, ItemScheduleData } from '../types/menu-schedule-api'

const mapItemScheduleToMenuItem = (item: {
  id: string
  item_id: string
  type: string
  action: string
  time: number
  end_time: number | null
  changed_data: Record<string, unknown>
  data_old: Record<string, unknown>
  status: string
  store_uid: string
  city_uid: string
  brand_uid: string
  company_uid: string
}): MenuItem => {
  const changedData = item.changed_data || {}

  return {
    id: (changedData.id as string) || item.id,
    code: item.item_id,
    name: (changedData.item_name as string) || '',
    action: item.action as 'CREATE' | 'UPDATE' | 'DELETE',
    originalItem: changedData as unknown as ItemScheduleData,
    scheduleId: item.id,
    item_class_uid: (changedData.item_class_uid as string) || undefined,
    item_type_uid: changedData.item_type_uid as string,
    unit_uid: changedData.unit_uid as string,
    unit_secondary_uid: (changedData.unit_secondary_uid as string) || undefined,
    ots_price: changedData.ots_price as number,
    customization_uid: (changedData.customization_uid as string) || undefined,
    cooking_time: changedData.time_cooking as number,
    allow_edit_price: Boolean(
      (changedData.extra_data as Record<string, unknown>)?.enable_edit_price
    ),
    is_buffet: Boolean((changedData.extra_data as Record<string, unknown>)?.is_buffet_item),
    is_service: Boolean(changedData.is_service),
    display_order: changedData.list_order as number,
    time_sale_date_week: changedData.time_sale_date_week as number,
    time_sale_hour_day: changedData.time_sale_hour_day as number
  }
}

export const useFetchMenuScheduleData = () => {
  const fetchMenuScheduleData = async (
    schedule: MenuSchedule
  ): Promise<MenuScheduleWithItems | null> => {
    try {
      const response = await menuScheduleApi.getMenuScheduleByParams({
        company_uid: schedule.company_uid,
        brand_uid: schedule.brand_uid,
        time: schedule.time,
        store_uid: schedule.store_uid,
        city_uid: schedule.city_uid
      })

      if (response.data?.item_schedules?.length > 0) {
        const menuItems = response.data.item_schedules.map(mapItemScheduleToMenuItem)

        return {
          ...schedule,
          menuItems
        }
      } else {
        toast.error('Không tìm thấy dữ liệu')
        return null
      }
    } catch (_error) {
      toast.error('Có lỗi xảy ra khi tải dữ liệu')
      return null
    }
  }

  return { fetchMenuScheduleData }
}
