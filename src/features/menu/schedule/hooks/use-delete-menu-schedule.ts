import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

export const useDeleteMenuSchedule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (scheduleData: {
      time: number
      store_uid: string
      city_uid: string
      brand_uid: string
      company_uid: string
    }) => menuScheduleApi.deleteMenuSchedule(scheduleData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-schedules'] })
      queryClient.invalidateQueries({ queryKey: ['menu-schedule'] })
      toast.success('Lịch trình menu đã được xóa thành công')
    },
    onError: () => {
      toast.error('Có lỗi xảy ra khi xóa menu schedule')
    }
  })
}
