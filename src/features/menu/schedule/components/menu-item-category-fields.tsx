import type { UseFormReturn } from 'react-hook-form'

import { useItemClassesData, useItemTypesData, useStoresData, useUnitsData } from '@/hooks/api'

import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import type { FormData } from '../utils/item-data-mapper'

interface MenuItemCategoryFieldsProps {
  form: UseFormReturn<FormData>
  cityUid?: string
  storeUid?: string
}

export function MenuItemCategoryFields({ form, cityUid, storeUid }: MenuItemCategoryFieldsProps) {
  const { data: itemClasses = [], isLoading: isLoadingItemClasses } = useItemClassesData({
    skip_limit: true,
    page: 1,
    results_per_page: 15000,
    enabled: !!cityUid
  })

  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true,
    ...(cityUid && { city_uid: cityUid }),
    enabled: !!cityUid
  })
  const { data: stores = [] } = useStoresData({
    params: { id: storeUid },
    enabled: !!storeUid && !!cityUid
  })
  const { data: units = [] } = useUnitsData()

  return (
    <div className='space-y-4'>
      {/* Món ăn kèm */}
      <FormField
        control={form.control}
        name='is_eat_with'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>Món ăn kèm</FormLabel>
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Không cập nhật số lượng món ăn kèm */}
      <FormField
        control={form.control}
        name='no_update_quantity'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Không cập nhật số lượng món ăn kèm
              </FormLabel>
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Nhóm */}
      <FormField
        control={form.control}
        name='item_class_uid'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>Nhóm</FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || ''}
                  disabled={isLoadingItemClasses}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder={'Chọn nhóm món'} />
                  </SelectTrigger>
                  <SelectContent>
                    {itemClasses?.map(itemClass => (
                      <SelectItem key={itemClass.id} value={itemClass.id}>
                        {itemClass.item_class_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Loại món */}
      <FormField
        control={form.control}
        name='item_type_uid'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>Loại món</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn loại món' />
                  </SelectTrigger>
                  <SelectContent>
                    {itemTypes?.map(itemType => (
                      <SelectItem key={itemType.id} value={itemType.id}>
                        {itemType.item_type_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Cửa hàng áp dụng */}
      <FormField
        control={form.control}
        name='store_uid'
        render={({ field }) => {
          const selectedStore = Array.isArray(stores)
            ? stores.find(store => store.id === storeUid)
            : null
          return (
            <FormItem>
              <div className='flex items-center gap-4'>
                <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                  Cửa hàng áp dụng <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value || ''} disabled>
                    <SelectTrigger className='flex-1'>
                      <SelectValue placeholder={selectedStore?.name || 'Chưa chọn cửa hàng'} />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedStore && (
                        <SelectItem key={selectedStore.id} value={selectedStore.id}>
                          {selectedStore.name}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )
        }}
      />

      {/* SKU */}
      <FormField
        control={form.control}
        name='sku'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>SKU</FormLabel>
              <FormControl>
                <Input placeholder='Nhập SKU' {...field} className='flex-1' />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Đơn vị tính */}
      <FormField
        control={form.control}
        name='unit_uid'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Đơn vị tính <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn đơn vị tính' />
                  </SelectTrigger>
                  <SelectContent>
                    {units?.map(unit => (
                      <SelectItem key={unit.id} value={unit.id}>
                        {unit.unit_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Đơn vị tính thứ 2 */}
      {/* <FormField
        control={form.control}
        name='unit_secondary_uid'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Đơn vị tính thứ 2
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Chọn đơn vị tính thứ 2' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=''>Không chọn</SelectItem>
                    {units?.map(unit => (
                      <SelectItem key={unit.id} value={unit.id}>
                        {unit.unit_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      /> */}
    </div>
  )
}
