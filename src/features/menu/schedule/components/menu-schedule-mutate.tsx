import { useState, useEffect } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { Search } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { createMenuSchedulePayload } from '@/lib/menu-schedule-api'

import { useCitiesData } from '@/hooks/api/use-removed-items'
import { useStoresData } from '@/hooks/api/use-stores'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

import { MenuSchedule } from '../data/schema'
import { useCreateMenuSchedule } from '../hooks/use-create-menu-schedule'
import { useUpdateItemSchedule } from '../hooks/use-update-item-schedule'
import type { MenuItem } from '../types/menu-schedule-api'
import { MenuItemScheduleDialog } from './menu-item-schedule-dialog'
import { MenuItemsTable } from './menu-items-table'
import { MenuScheduleFormFields } from './menu-schedule-form-fields'

const formSchema = z.object({
  start_date: z.date({ required_error: 'Ngày bắt đầu là bắt buộc' }),
  end_date: z.date().optional(),
  city_uid: z.string().min(1, 'Thành phố là bắt buộc'),
  store_uid: z.string().min(1, 'Cửa hàng là bắt buộc')
})

type FormData = z.infer<typeof formSchema>

interface MenuScheduleMutateProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: MenuSchedule & {
    menuItems?: MenuItem[]
    id?: string
    company_uid?: string
    brand_uid?: string
    store_uid?: string
    city_uid?: string
    time?: number
    end_time?: number | null
  }
}

export function MenuScheduleMutate({ open, onOpenChange, currentRow }: MenuScheduleMutateProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const createMenuScheduleMutation = useCreateMenuSchedule()
  const updateItemScheduleMutation = useUpdateItemSchedule()

  const isUpdateMode = !!currentRow?.menuItems

  const { data: citiesData = [] } = useCitiesData()
  const { data: storesData = [] } = useStoresData({
    enabled: open
  })

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      start_date: currentRow?.time
        ? new Date(currentRow.time)
        : new Date(new Date().setDate(new Date().getDate() + 1)),
      end_date: currentRow?.end_time ? new Date(currentRow.end_time) : undefined,
      city_uid: currentRow?.city_uid || '',
      store_uid: currentRow?.store_uid || ''
    }
  })

  useEffect(() => {
    if (currentRow && currentRow.menuItems) {
      setMenuItems(currentRow.menuItems)

      form.reset({
        start_date: currentRow.time ? new Date(currentRow.time) : new Date(),
        end_date: currentRow.end_time ? new Date(currentRow.end_time) : undefined,
        city_uid: currentRow.city_uid || '',
        store_uid: currentRow.store_uid || ''
      })
    } else {
      setMenuItems([])

      form.reset({
        start_date: new Date(new Date().setDate(new Date().getDate() + 1)),
        end_date: undefined,
        city_uid: '',
        store_uid: ''
      })
    }
  }, [currentRow, form])

  const selectedCityUid = form.watch('city_uid')
  const selectedStoreUid = form.watch('store_uid')

  const filteredStores = selectedCityUid
    ? storesData.filter(store => {
        const selectedCity = citiesData.find(city => city.id === selectedCityUid)
        return selectedCity && store.cityId === selectedCity.city_id
      })
    : []

  const handleAddMenuItem = (e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    setIsDialogOpen(true)
  }

  const handleDialogConfirm = (items: MenuItem[]) => {
    if (selectedItem) {
      const updatedItems = menuItems.map(item => (item.id === selectedItem.id ? items[0] : item))
      setMenuItems(updatedItems)
    } else {
      setMenuItems([...menuItems, ...items])
    }
    setIsDialogOpen(false)
    setSelectedItem(null)
  }

  const handleRowClick = (item: MenuItem) => {
    setSelectedItem(item)
    setIsDialogOpen(true)
  }

  const handleRemoveMenuItem = (id: string) => {
    setMenuItems(menuItems.filter(item => item.id !== id))
  }

  const handleSubmit = async (data: FormData) => {
    if (menuItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một món')
      return
    }

    if (isUpdateMode) {
      const getScheduleId = () => {
        const allItems = [...menuItems, ...(currentRow?.menuItems || [])]
        return allItems.find(item => item.scheduleId)?.scheduleId
      }

      const scheduleData = {
        company_uid: currentRow?.company_uid || company?.id || '',
        brand_uid: currentRow?.brand_uid || selectedBrand?.id || '',
        store_uid: currentRow?.store_uid || data.store_uid,
        city_uid: currentRow?.city_uid || data.city_uid,
        time: currentRow?.time || data.start_date.getTime(),
        end_time: currentRow?.end_time || data.end_date?.getTime() || null,
        schedule_id: getScheduleId()
      }

      updateItemScheduleMutation.mutate(
        { menuItems, scheduleData },
        {
          onSuccess: () => {
            onOpenChange(false)
          }
        }
      )
    } else {
      const payload = createMenuSchedulePayload(
        {
          company_uid: company?.id || '',
          brand_uid: selectedBrand?.id || '',
          city_uid: data.city_uid,
          store_uid: data.store_uid,
          start_date: data.start_date,
          end_date: data.end_date
        },
        menuItems
      )

      createMenuScheduleMutation.mutate(payload, {
        onSuccess: () => {
          onOpenChange(false)
        }
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto sm:max-w-4xl'>
        <DialogHeader>
          <DialogTitle>{isUpdateMode ? 'Cập nhật lịch thay đổi' : 'Tạo lịch thay đổi'}</DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
              <div className='rounded-lg border p-4'>
                <h3 className='mb-4 font-medium'>Thông tin cơ bản</h3>

                <MenuScheduleFormFields
                  form={form}
                  citiesData={citiesData.map(city => ({ id: city.id, name: city.city_name }))}
                  storesData={filteredStores.map(store => ({
                    id: store.id,
                    name: store.name
                  }))}
                  isUpdateMode={isUpdateMode}
                />

                <div className='mt-4'>
                  <p className='text-muted-foreground text-sm'>Thức đơn thay đổi theo cửa hàng</p>
                </div>
              </div>

              <div className='rounded-lg border p-4'>
                <h3 className='mb-4 font-medium'>Món liên lịch thay đổi</h3>

                <div className='mb-4 flex gap-2'>
                  <div className='flex-1'>
                    <Input
                      placeholder={
                        selectedCityUid
                          ? 'Nhập tên món hoặc thao tác'
                          : 'Vui lòng chọn thành phố trước'
                      }
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      onKeyDown={e => e.key === 'Enter' && selectedCityUid && handleAddMenuItem()}
                      disabled={!selectedCityUid}
                    />
                  </div>
                  <Button
                    type='button'
                    onClick={handleAddMenuItem}
                    size='sm'
                    disabled={!selectedCityUid}
                  >
                    <Search className='mr-2 h-4 w-4' />
                    Thêm món
                  </Button>
                </div>

                <MenuItemsTable
                  menuItems={menuItems}
                  onRowClick={handleRowClick}
                  onRemoveItem={handleRemoveMenuItem}
                />
              </div>
            </form>
          </Form>

          {isDialogOpen && (
            <MenuItemScheduleDialog
              open={isDialogOpen}
              onOpenChange={setIsDialogOpen}
              onConfirm={handleDialogConfirm}
              cityUid={selectedCityUid}
              storeUid={selectedStoreUid}
              selectedItem={selectedItem}
            />
          )}
        </div>

        <DialogFooter className='gap-2'>
          <Button type='button' variant='outline'>
            Thêm từ file
          </Button>
          <Button variant='outline' type='button' onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button
            type='submit'
            disabled={createMenuScheduleMutation.isPending || updateItemScheduleMutation.isPending}
            onClick={() => form.handleSubmit(handleSubmit)()}
          >
            Lưu
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
