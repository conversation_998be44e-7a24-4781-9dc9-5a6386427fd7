import { MoreHorizontal, Pen, Trash2 } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

interface MenuScheduleActionsDropdownProps {
  onEdit: () => void
  onDelete: () => void
  isLoading?: boolean
}

export function MenuScheduleActionsDropdown({ 
  onEdit, 
  onDelete, 
  isLoading = false 
}: MenuScheduleActionsDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant='ghost' 
          className='data-[state=open]:bg-muted flex h-8 w-8 p-0'
          disabled={isLoading}
        >
          <MoreHorizontal className='h-4 w-4' />
          <span className='sr-only'>Mở menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[160px]'>
        <DropdownMenuItem onClick={onEdit} disabled={isLoading}>
          <Pen className='mr-2 h-4 w-4' />
          Chỉnh sửa
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={onDelete} className='text-red-600' disabled={isLoading}>
          <Trash2 className='mr-2 h-4 w-4' />
          Xóa
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
