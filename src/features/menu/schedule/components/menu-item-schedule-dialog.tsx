import React, { useState } from 'react'

import { useAuthStore } from '@/stores/authStore'

import { useItemsData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'

import { useMenuItemForm } from '../hooks/use-menu-item-form'
import type { MenuItemFormData } from '../schemas/menu-item-form-schema'
import type { MenuItem } from '../types/menu-schedule-api'
import { MenuItemActionSelector } from './menu-item-action-selector'
import { MenuItemAdvancedFields } from './menu-item-advanced-fields'
import { MenuItemBasicFields } from './menu-item-basic-fields'
import { MenuItemCategoryFields } from './menu-item-category-fields'
import { MenuItemSelector } from './menu-item-selector'

interface MenuItemScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (items: MenuItem[]) => void
  cityUid?: string
  storeUid?: string
  selectedItem?: MenuItem | null
}

export function MenuItemScheduleDialog({
  open,
  onOpenChange,
  onConfirm,
  cityUid,
  storeUid,
  selectedItem
}: MenuItemScheduleDialogProps) {
  const [selectedAction, setSelectedAction] = useState('')
  const [selectedItemId, setSelectedItemId] = useState('')

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]
  const { form, createMenuItem } = useMenuItemForm({ selectedItem, open })

  const { data: items = [] } = useItemsData({
    params: {
      company_uid: company?.id,
      brand_uid: selectedBrand?.id,
      city_uid: cityUid,
      store_uid: storeUid
    },
    enabled: !!(company?.id && selectedBrand?.id && (cityUid || storeUid))
  })

  React.useEffect(() => {
    if (selectedItem) {
      setSelectedAction(selectedItem.action)
      setSelectedItemId(selectedItem.id)
    } else if (open) {
      setSelectedAction('')
      setSelectedItemId('')
    }
  }, [selectedItem, open])

  const handleConfirm = (data: MenuItemFormData) => {
    if (!selectedAction) return
    if (selectedAction === 'UPDATE' && !selectedItemId) return

    const item = createMenuItem(data, selectedAction, selectedItemId)
    onConfirm([item])

    form.reset()
    setSelectedAction('')
    setSelectedItemId('')
    onOpenChange(false)
  }

  const handleItemSelect = React.useCallback(
    (itemId: string) => {
      setSelectedItemId(itemId)
      const item = items.find(i => i.id === itemId)
      if (item) {
        const itemData = {
          item_name: item.item_name,
          item_id: item.item_id,
          ots_price: item.ots_price || 0,
          item_class_uid: item.item_class_uid || '',
          item_type_uid: item.item_type_uid || '',
          unit_uid: item.unit_uid || '',
          unit_secondary_uid: item.unit_secondary_uid || ''
        }

        Object.entries(itemData).forEach(([key, value]) => {
          form.setValue(key as keyof MenuItemFormData, value)
        })
      }
    },
    [items, form]
  )

  const showFormFields = React.useMemo(() => {
    const action = selectedItem?.action || selectedAction
    return action === 'CREATE' || action === 'UPDATE'
  }, [selectedItem?.action, selectedAction])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto sm:max-w-4xl'>
        <DialogHeader>
          <DialogTitle>Quản lý món ăn</DialogTitle>
          <DialogDescription>Thêm, sửa hoặc xóa món ăn trong menu schedule</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleConfirm)} className='space-y-6'>
            <div className='space-y-6'>
              {/* Thông tin cơ bản */}
              <div>
                <h3 className='mb-4 text-sm font-medium text-gray-700'>Thông tin cơ bản</h3>

                {/* Thao tác */}
                <MenuItemActionSelector
                  selectedAction={selectedAction}
                  onActionChange={setSelectedAction}
                  selectedItem={selectedItem}
                />
              </div>

              {selectedAction === 'UPDATE' ||
                (selectedAction === 'DELETE' && !selectedItem && (
                  <MenuItemSelector
                    selectedItemId={selectedItemId}
                    onItemSelect={handleItemSelect}
                    selectedItem={selectedItem}
                    items={items}
                  />
                ))}

              {showFormFields && (
                <div className='space-y-6 border-t pt-4'>
                  <MenuItemBasicFields form={form} />
                  <MenuItemCategoryFields form={form} cityUid={cityUid} storeUid={storeUid} />
                  <MenuItemAdvancedFields form={form} cityUid={cityUid} storeUid={storeUid} />
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type='button' variant='outline' onClick={() => onOpenChange(false)}>
                Hủy
              </Button>
              <Button type='submit' disabled={!selectedAction}>
                Xác nhận
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
