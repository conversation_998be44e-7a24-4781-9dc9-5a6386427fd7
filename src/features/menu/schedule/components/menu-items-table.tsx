import { Trash2 } from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

interface MenuItem {
  id: string
  code: string
  name: string
  action: 'CREATE' | 'UPDATE' | 'DELETE'
}

interface MenuItemsTableProps {
  menuItems: MenuItem[]
  onRowClick: (item: MenuItem) => void
  onRemoveItem: (id: string) => void
}

export function MenuItemsTable({ menuItems, onRowClick, onRemoveItem }: MenuItemsTableProps) {
  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Mã món</TableHead>
            <TableHead>Tên món</TableHead>
            <TableHead>Thao tác</TableHead>
            <TableHead className='w-[50px]'></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {menuItems.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className='text-center text-muted-foreground'>
                Chưa có món nào được thêm
              </TableCell>
            </TableRow>
          ) : (
            menuItems.map(item => (
              <TableRow
                key={item.id}
                className='cursor-pointer hover:bg-gray-50'
                onClick={() => onRowClick(item)}
              >
                <TableCell className='font-medium'>{item.code}</TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${
                      item.action === 'CREATE'
                        ? 'bg-green-50 text-green-700 ring-green-600/20'
                        : item.action === 'UPDATE'
                          ? 'bg-blue-50 text-blue-700 ring-blue-600/20'
                          : 'bg-red-50 text-red-700 ring-red-600/20'
                    }`}
                  >
                    {item.action}
                  </span>
                </TableCell>
                <TableCell>
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={e => {
                      e.stopPropagation()
                      onRemoveItem(item.id)
                    }}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
