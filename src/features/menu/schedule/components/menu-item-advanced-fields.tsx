import type { UseFormReturn } from 'react-hook-form'

import { useCustomizationsData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'

import type { FormData } from '../utils/item-data-mapper'

interface MenuItemAdvancedFieldsProps {
  form: UseFormReturn<FormData>
  cityUid?: string
  storeUid?: string
}

export function MenuItemAdvancedFields({ form, cityUid, storeUid }: MenuItemAdvancedFieldsProps) {
  const { data: customizations = [], isLoading: isLoadingCustomizations } = useCustomizationsData({
    skip_limit: true,
    ...(storeUid && { store_uid: storeUid }),
    ...(cityUid && !storeUid && { list_city_uid: [cityUid] }),
    enabled: !!cityUid
  })

  return (
    <div className='space-y-4'>
      {/* Vat món ăn */}
      <FormField
        control={form.control}
        name='vat_rate'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>Vat món ăn</FormLabel>
              <div className='flex flex-1 items-center gap-2'>
                <FormControl>
                  <Input type='number' placeholder='0' {...field} className='flex-1' />
                </FormControl>
                <span className='text-sm text-gray-500'>%</span>
              </div>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Thời gian chế biến (phút) */}
      <FormField
        control={form.control}
        name='cooking_time'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Thời gian chế biến (phút)
              </FormLabel>
              <FormControl>
                <Input type='number' placeholder='0' {...field} className='flex-1' />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Cấu hình sửa giá, nhập số lượng */}
      <div className='flex items-center gap-4'>
        <FormLabel className='w-40 text-sm font-medium text-gray-700'>
          Cấu hình sửa giá, nhập số lượng
        </FormLabel>

        <div className='flex flex-col gap-2'>
          <FormField
            control={form.control}
            name='allow_edit_price'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-2'>
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className='text-sm text-gray-700'>Cho phép sửa giá khi bán</FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Yêu cầu nhập số lượng khi gọi đồ */}
          <FormField
            control={form.control}
            name='require_quantity_input'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-2'>
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className='text-sm text-gray-700'>
                    Yêu cầu nhập số lượng khi gọi đồ
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Cho phép bỏ món mà không cần quyền áp dụng */}
          <FormField
            control={form.control}
            name='allow_remove_without_permission'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center gap-2'>
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className='text-sm text-gray-700'>
                    Cho phép bỏ món mà không cần quyền áp dụng
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Cấu hình món ăn */}
      <FormField
        control={form.control}
        name='is_featured'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Cấu hình món ăn
              </FormLabel>
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Cấu hình món ăn là vé buffet */}
      <FormField
        control={form.control}
        name='is_buffet'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Cấu hình món ăn là vé buffet
              </FormLabel>
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Công thức inQr cho máy pha trà */}
      <FormField
        control={form.control}
        name='inqr_formula'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-start gap-4'>
              <FormLabel className='mt-2 w-40 text-sm font-medium text-gray-700'>
                Công thức inQr cho máy pha trà
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Nhập công thức inQR'
                  {...field}
                  className='min-h-[80px] flex-1'
                />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Cấu hình món dịch vụ */}
      <div className='flex items-center gap-4'>
        <FormLabel className='w-40 text-sm font-medium text-gray-700'>
          Cấu hình món dịch vụ
        </FormLabel>

        <FormField
          control={form.control}
          name='is_service'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-center gap-2'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Cấu hình giá theo nguồn */}
      <div className='space-y-3'>
        <div className='flex items-center justify-between'>
          <FormLabel className='text-sm font-medium text-gray-700'>
            Cấu hình giá theo nguồn
          </FormLabel>
          <Button
            type='button'
            variant='outline'
            size='sm'
            className='bg-blue-600 text-white hover:bg-blue-700'
          >
            Thêm nguồn
          </Button>
        </div>
      </div>

      {/* Customization */}
      <FormField
        control={form.control}
        name='customization'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Customization
              </FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || ''}
                  disabled={isLoadingCustomizations}
                >
                  <SelectTrigger className='flex-1'>
                    <SelectValue placeholder='Tìm kiếm' />
                  </SelectTrigger>
                  <SelectContent>
                    {customizations?.map(customization => (
                      <SelectItem key={customization.id} value={customization.id}>
                        {customization.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Khung thời gian bán */}
      <div className='space-y-4'>
        <FormLabel className='text-sm font-medium text-gray-700'>Khung thời gian bán</FormLabel>

        {/* Chọn ngày */}
        <FormField
          control={form.control}
          name='selected_days'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm text-gray-600'>Chọn ngày</FormLabel>
              <div className='grid grid-cols-7 gap-2'>
                {['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'].map(
                  (day, index) => {
                    const isSelected = Array.isArray(field.value) ? field.value[index] : false
                    return (
                      <Button
                        key={day}
                        type='button'
                        variant={isSelected ? 'default' : 'outline'}
                        size='sm'
                        className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                        onClick={() => {
                          const newValue = Array.isArray(field.value)
                            ? [...field.value]
                            : [false, false, false, false, false, false, false]
                          newValue[index] = !newValue[index]
                          field.onChange(newValue)
                        }}
                      >
                        {day}
                      </Button>
                    )
                  }
                )}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Chọn giờ */}
        <FormField
          control={form.control}
          name='selected_hours'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm text-gray-600'>Chọn giờ</FormLabel>
              <div className='grid grid-cols-10 gap-2'>
                {Array.from({ length: 24 }, (_, i) => `${i}h`).map(hour => {
                  const isSelected = field.value && field.value[hour]
                  return (
                    <Button
                      key={hour}
                      type='button'
                      variant={isSelected ? 'default' : 'outline'}
                      size='sm'
                      className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                      onClick={() => {
                        const newValue = { ...field.value }
                        newValue[hour] = !newValue[hour]
                        field.onChange(newValue)
                      }}
                    >
                      {hour}
                    </Button>
                  )
                })}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Thứ tự hiển thị */}
      <FormField
        control={form.control}
        name='display_order'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Thứ tự hiển thị
              </FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='Nhập số thứ tự hiển thị'
                  {...field}
                  className='flex-1'
                />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
