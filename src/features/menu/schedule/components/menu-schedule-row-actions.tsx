import { useState } from 'react'

import { Row } from '@tanstack/react-table'

import { useMenuSchedule } from '../context'
import { MenuSchedule } from '../data'
import { useFetchMenuScheduleData } from '../hooks/use-fetch-menu-schedule-data'
import { MenuScheduleActionsDropdown } from './menu-schedule-actions-dropdown'

interface MenuScheduleRowActionsProps {
  row: Row<MenuSchedule>
}

export function MenuScheduleRowActions({ row }: MenuScheduleRowActionsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { setOpen, setCurrentRow } = useMenuSchedule()
  const { fetchMenuScheduleData } = useFetchMenuScheduleData()
  const schedule = row.original

  const handleEdit = async () => {
    setIsLoading(true)
    try {
      const result = await fetchMenuScheduleData(schedule)
      if (result) {
        setCurrentRow(result)
        setOpen('update')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = () => {
    setCurrentRow(schedule)
    setOpen('delete')
  }

  return (
    <MenuScheduleActionsDropdown
      onEdit={handleEdit}
      onDelete={handleDelete}
      isLoading={isLoading}
    />
  )
}
