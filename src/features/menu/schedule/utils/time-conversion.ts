// Convert bit flags to boolean arrays
// Array mapping: [<PERSON><PERSON><PERSON> 2, <PERSON><PERSON><PERSON> 3, <PERSON><PERSON><PERSON> 4, <PERSON><PERSON><PERSON> 5, <PERSON><PERSON><PERSON> 6, <PERSON><PERSON><PERSON> 7, <PERSON><PERSON>]
// time_sale_date_week = 16 = Thứ 4 → bit 4 = array index 2
export const convertDateWeekToArray = (dateWeek: number): boolean[] => {
  const days = [false, false, false, false, false, false, false]
  
  const bitToIndex = [6, 0, 1, 3, 2, 4, 5] // bit 4→index 2 (Thứ 4)
  
  for (let bit = 0; bit < 7; bit++) {
    if (dateWeek & (1 << bit)) {
      const arrayIndex = bitToIndex[bit]
      if (arrayIndex < 7) {
        days[arrayIndex] = true
      }
    }
  }
  
  return days
}

export const convertArrayToDateWeek = (days: boolean[]): number => {
  let dateWeek = 0
  const indexToBit = [1, 2, 4, 3, 5, 6, 0] // index 2→bit 4 (T<PERSON><PERSON> 4)
  
  for (let i = 0; i < 7; i++) {
    if (days[i]) {
      dateWeek |= 1 << indexToBit[i]
    }
  }
  
  return dateWeek
}

export const convertHourDayToObject = (hourDay: number): Record<string, boolean> => {
  const hours: Record<string, boolean> = {}
  for (let i = 0; i < 24; i++) {
    hours[`${i}h`] = (hourDay & (1 << i)) !== 0
  }
  return hours
}

export const convertObjectToHourDay = (hours: Record<string, boolean>): number => {
  let hourDay = 0
  for (let i = 0; i < 24; i++) {
    if (hours[`${i}h`]) {
      hourDay |= 1 << i
    }
  }
  return hourDay
}
