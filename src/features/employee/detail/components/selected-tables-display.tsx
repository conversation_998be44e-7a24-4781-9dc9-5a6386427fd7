import { useMemo } from 'react'

import { useTablesData } from '@/hooks/api'

interface SelectedTablesDisplayProps {
  selectedTables: string[]
  storeId: string
  brandId: string
}

export function SelectedTablesDisplay({
  selectedTables,
  storeId,
  brandId
}: SelectedTablesDisplayProps) {
  const { data: tables = [] } = useTablesData({
    storeUid: storeId,
    enabled: !!storeId && !!brandId
  })

  const selectedTableInfo = useMemo(() => {
    if (!selectedTables.length || !tables.length) return []

    return selectedTables
      .map(tableId => {
        const table = tables.find(t => t.id === tableId)
        return table
          ? {
              id: table.id,
              name: table.table_name,
              areaName: table.area.area_name
            }
          : null
      })
      .filter(Boolean)
      .sort((a, b) => a!.areaName.localeCompare(b!.areaName))
  }, [selectedTables, tables])

  if (selectedTableInfo.length === 0) {
    return null
  }

  const tablesByArea = selectedTableInfo.reduce(
    (acc, table) => {
      if (!table) return acc

      if (!acc[table.areaName]) {
        acc[table.areaName] = []
      }
      acc[table.areaName].push(table.name)
      return acc
    },
    {} as Record<string, string[]>
  )

  return (
    <div className='space-y-3'>
      <div className='rounded-md border bg-gray-50'>
        {Object.entries(tablesByArea).map(([areaName, tableNames]) => (
          <div
            key={areaName}
            className='grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50'
          >
            <div className='p-3 text-sm font-medium text-gray-700'>{areaName}</div>
            <div className='p-3 text-sm text-gray-600'>{tableNames.join(', ')}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
