import { useState, useRef, useCallback } from 'react'

import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useAuthStore } from '@/stores/authStore'

import { api } from '@/lib/api'

import { useFilteredPaymentMethodsData } from '@/hooks/api'

import {
  createExcelWorkbook,
  createMainWorksheet,
  createExampleWorksheet,
  transformImportDataToApiFormat
} from '../utils'

export interface ImportSaleChannelData {
  source_id: string
  commission: number
  not_show_partner_bill: number
  use_order_online: number
  exclude_ship: number
  payment_type: string
  payment_method_id: string
  require_tran_no: number
  marketing_partner_cost_type: string
  marketing_partner_cost: number
  voucher_run_partner: string
  marketing_partner_cost_from_date: string
  marketing_partner_cost_to_date: string
  marketing_partner_cost_date_week: number
  marketing_partner_cost_hour_day: number
}

interface ValidationError {
  row: number
  message: string
}

export function useImportSaleChannel() {
  const [isLoading, setIsLoading] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [parsedData, setParsedData] = useState<ImportSaleChannelData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [showValidationModal, setShowValidationModal] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  // Fetch payment methods for validation
  const { data: paymentMethods = [] } = useFilteredPaymentMethodsData({
    storeUid: 'all' // Get all payment methods for validation
  })

  // Validation function
  const validateImportData = (data: ImportSaleChannelData[]): ValidationError[] => {
    const errors: ValidationError[] = []
    const seenSources = new Set<string>()
    const validPaymentMethods = new Set(paymentMethods.map(pm => pm.code))

    data.forEach((item, index) => {
      const rowNumber = index + 2 // +2 because Excel rows start at 1 and we skip header

      // Check for duplicate sources
      if (seenSources.has(item.source_id)) {
        errors.push({
          row: rowNumber,
          message: `Mã nguồn "${item.source_id}" bị trùng lặp`
        })
      } else {
        seenSources.add(item.source_id)
      }

      // Check for valid payment method (only for POSTPAID)
      if (item.payment_type === 'POSTPAID' && item.payment_method_id) {
        if (!validPaymentMethods.has(item.payment_method_id)) {
          errors.push({
            row: rowNumber,
            message: `Phương thức thanh toán "${item.payment_method_id}" không hợp lệ`
          })
        }
      }

      // Check required fields
      if (!item.source_id || item.source_id.trim() === '') {
        errors.push({
          row: rowNumber,
          message: 'Mã nguồn không được để trống'
        })
      }

      if (
        !item.payment_type ||
        (item.payment_type !== 'PREPAID' && item.payment_type !== 'POSTPAID')
      ) {
        errors.push({
          row: rowNumber,
          message: 'Hình thức thanh toán phải là PREPAID hoặc POSTPAID'
        })
      }
    })

    return errors
  }

  const handleFileUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast.error('Vui lòng chọn file Excel (.xlsx hoặc .xls)')
      return
    }

    setSelectedFile(file)
    parseExcelFile(file)
  }

  const parseExcelFile = (file: File) => {
    setIsLoading(true)
    const reader = new FileReader()

    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        // Skip header row and parse data
        const rows = jsonData.slice(1) as any[]
        const parsed: ImportSaleChannelData[] = rows
          .filter(row => row[0]) // Filter out empty rows
          .map(row => ({
            source_id: row[0] || '',
            commission: parseFloat(row[1]) || 0,
            not_show_partner_bill: parseInt(row[2]) || 0,
            use_order_online: parseInt(row[3]) || 0,
            exclude_ship: parseInt(row[4]) || 0,
            payment_type: row[5] || 'POSTPAID',
            payment_method_id: row[6] || '',
            require_tran_no: parseInt(row[7]) || 0,
            marketing_partner_cost_type: row[8] || 'AMOUNT',
            marketing_partner_cost: parseFloat(row[9]) || 0,
            voucher_run_partner: row[10] || '',
            marketing_partner_cost_from_date: row[11] || '',
            marketing_partner_cost_to_date: row[12] || '',
            marketing_partner_cost_date_week: parseInt(row[13]) || 0,
            marketing_partner_cost_hour_day: parseInt(row[14]) || 0
          }))

        // Validate data
        const errors = validateImportData(parsed)
        setValidationErrors(errors)

        if (errors.length > 0) {
          // Show validation modal instead of toast
          setValidationErrors(errors)
          setShowValidationModal(true)
          return
        }

        // If validation passes, close validation modal and show preview
        setShowValidationModal(false)
        setValidationErrors([])
        setParsedData(parsed)
        setShowImportParsedData(true)
        toast.success(`Đã phân tích ${parsed.length} kênh bán hàng từ file`)
      } catch (_error) {
        toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      } finally {
        setIsLoading(false)
      }
    }

    reader.onerror = () => {
      toast.error('Lỗi khi đọc file')
      setIsLoading(false)
    }

    reader.readAsArrayBuffer(file)
  }

  const downloadTemplate = async (storeId: string) => {
    if (!company?.id || !selectedBrand?.id || !storeId) {
      toast.error('Vui lòng chọn cửa hàng trước khi tải file')
      return
    }

    try {
      setIsLoading(true)

      // Fetch existing channels for the selected store
      const response = await api.get('/mdata/v1/channels', {
        params: {
          skip_limit: 'true',
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          store_uid: storeId
        }
      })

      const channels = Array.isArray(response.data?.data) ? response.data.data : []

      // Create Excel workbook with proper formatting using ExcelJS
      const workbook = createExcelWorkbook()
      createMainWorksheet(workbook, channels)
      createExampleWorksheet(workbook, channels)

      // Download the file using ExcelJS
      try {
        // Generate buffer from ExcelJS workbook
        const buffer = await workbook.xlsx.writeBuffer()

        // Create blob and download
        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        // Create download link
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = 'import-sale-channel.xlsx'
        link.style.display = 'none'
        document.body.appendChild(link)

        // Trigger download
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (_error) {
        console.log('ExcelJS download failed:', _error)
        toast.error('Lỗi khi tạo file Excel')
        return
      }
    } catch (_error) {
      toast.error('Lỗi khi tải file dữ liệu kênh bán hàng')
    } finally {
      setIsLoading(false)
    }
  }

  const importData = async (storeId: string) => {
    if (!company?.id || !selectedBrand?.id || !storeId || parsedData.length === 0) {
      toast.error('Không có dữ liệu để import')
      return
    }

    try {
      setIsLoading(true)

      // Transform data to API format
      const importPayload = transformImportDataToApiFormat(
        parsedData,
        company.id,
        selectedBrand.id,
        storeId
      )

      // Import each channel
      for (const channelData of importPayload) {
        await api.post('/mdata/v1/channels', [channelData])
      }

      toast.success(`Đã import thành công ${importPayload.length} kênh bán hàng`)
      return true
    } catch (_error) {
      toast.error('Lỗi khi import dữ liệu')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const handleValidationRetry = () => {
    // Close validation modal and clear errors, then open file picker
    setShowValidationModal(false)
    setValidationErrors([])
    // Reset selected file to allow user to select again
    setSelectedFile(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    handleFileUpload()
  }

  const handleValidationModalClose = (open: boolean) => {
    if (!open) {
      setShowValidationModal(false)
      setValidationErrors([])
      // Reset selected file to allow user to select again
      setSelectedFile(null)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const resetState = useCallback(() => {
    setSelectedFile(null)
    setParsedData([])
    setShowImportParsedData(false)
    setValidationErrors([])
    setShowValidationModal(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  return {
    isLoading,
    selectedFile,
    parsedData,
    showImportParsedData,
    validationErrors,
    showValidationModal,
    fileInputRef,
    handleFileUpload,
    handleFileChange,
    handleValidationRetry,
    handleValidationModalClose,
    downloadTemplate,
    importData,
    resetState
  }
}
