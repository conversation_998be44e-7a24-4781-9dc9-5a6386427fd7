import { FilterDropdown } from '@/components/filter-dropdown'

interface Store {
  id: string
  name: string
}

interface StoreSelectProps {
  value: string
  onValueChange: (value: string) => void
  stores?: Store[]
  isLoading?: boolean
  placeholder?: string
  className?: string
}

export function StoreSelect({
  value,
  onValueChange,
  stores,
  isLoading,
  placeholder = 'Tất cả điểm',
  className = 'w-48'
}: StoreSelectProps) {
  const storeOptions =
    stores?.map(store => ({
      value: store.id,
      label: store.name
    })) || []

  return (
    <FilterDropdown
      value={value}
      onValueChange={onValueChange}
      options={storeOptions}
      isLoading={isLoading}
      placeholder={placeholder}
      className={className}
      allOptionLabel='Tất cả điểm'
      loadingText='Đang tải...'
      emptyText='Không có cửa hàng'
    />
  )
}
