import { useState, useEffect } from 'react'

import { Download, Upload } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'

import { useStoresData } from '@/hooks/api/use-stores'

import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { PosModal } from '@/components/pos/modal'

import { useImportSaleChannel } from '../../hooks/use-import-sale-channel'
import { ImportPreviewTable } from './import-preview-table'
import { ValidationErrorModal } from './validation-error-modal'

interface ImportSaleChannelModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function ImportSaleChannelModal({
  open,
  onOpenChange,
  onSuccess
}: ImportSaleChannelModalProps) {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  // Fetch stores data
  const { data: storesData = [] } = useStoresData({
    params: {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || ''
    },
    enabled: !!company?.id && !!selectedBrand?.id
  })

  const {
    isLoading,
    selectedFile,
    parsedData,
    showImportParsedData,
    validationErrors,
    showValidationModal,
    fileInputRef,
    handleFileUpload,
    handleFileChange,
    handleValidationRetry,
    handleValidationModalClose,
    downloadTemplate,
    importData,
    resetState
  } = useImportSaleChannel()

  // Reset state when modal closes
  useEffect(() => {
    if (!open && !isLoading) {
      setSelectedStoreId('')
      resetState()
    }
  }, [open, resetState, isLoading])

  const handleDownloadTemplate = async () => {
    if (!selectedStoreId) {
      return
    }

    try {
      await downloadTemplate(selectedStoreId)
    } catch (error) {
      // Error is already handled in the hook
      console.error('Download template error:', error)
    }
  }

  const handleConfirm = async () => {
    if (showImportParsedData) {
      try {
        // Import data
        const success = await importData(selectedStoreId)
        if (success) {
          onSuccess?.()
          onOpenChange(false)
        }
      } catch (error) {
        // Error is already handled in the hook
        console.error('Import data error:', error)
      }
    } else {
      // Continue to next step - this should not happen as we auto-advance
      return
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const getConfirmText = () => {
    if (showImportParsedData) {
      return 'Tải file lên'
    }
    return 'Tiếp tục'
  }

  const getSelectedStoreName = () => {
    const selectedStore = storesData.find(store => store.id === selectedStoreId)
    return selectedStore?.name || 'cửa hàng'
  }

  const isConfirmDisabled = () => {
    if (showImportParsedData) {
      return parsedData.length === 0
    }
    return false
  }

  return (
    <>
      <PosModal
        title='Thêm kênh bán hàng cho cửa hàng'
        open={(open && !showValidationModal) || showImportParsedData}
        onOpenChange={onOpenChange}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        confirmText={getConfirmText()}
        cancelText='Hủy'
        centerTitle={true}
        maxWidth={showImportParsedData ? 'sm:max-w-6xl' : 'sm:max-w-[600px]'}
        isLoading={isLoading}
        confirmDisabled={isConfirmDisabled()}
        hideButtons={!showImportParsedData}
      >
        <div className='space-y-6'>
          {!showImportParsedData ? (
            <>
              {/* Bước 1: Chọn cửa hàng */}
              <div className='space-y-3 rounded-lg bg-gray-50 p-4'>
                <div className='text-lg font-medium'>Bước 1. Chọn cửa hàng</div>
                <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Chọn cửa hàng' />
                  </SelectTrigger>
                  <SelectContent>
                    {storesData.map(store => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Bước 2: Tải file dữ liệu kênh bán hàng đã có */}
              <div className='space-y-3 rounded-lg bg-gray-50 p-4'>
                <div className='text-lg font-medium'>
                  Bước 2. Tải file dữ liệu kênh bán hàng đã có.
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-gray-600'>Tải xuống</span>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => {
                      handleDownloadTemplate()
                    }}
                    disabled={!selectedStoreId || isLoading}
                    className='flex items-center gap-2'
                  >
                    <Download className='h-4 w-4' />
                  </Button>
                </div>
              </div>

              {/* Bước 3: Thêm cấu hình vào file */}
              <div className='space-y-3 rounded-lg bg-gray-50 p-4'>
                <div className='text-lg font-medium'>Bước 3. Thêm cấu hình vào file</div>
                <div className='text-sm text-gray-600'>
                  Thêm hoặc sửa thông tin kênh bán hàng dựa vào sheet mẫu.
                </div>
              </div>

              {/* Bước 4: Tải file lên */}
              <div className='space-y-3 rounded-lg bg-gray-50 p-4'>
                <div className='flex items-center justify-between'>
                  <div>
                    <div className='text-lg font-medium'>Bước 4. Tải file lên</div>
                    <div className='text-sm text-gray-600'>
                      Sau khi đã điền đầy đủ bạn có thể tải file lên
                    </div>
                  </div>
                  <Button
                    variant='outline'
                    onClick={handleFileUpload}
                    disabled={isLoading}
                    className='flex items-center gap-2'
                  >
                    <Upload className='h-4 w-4' />
                    Tải file lên
                  </Button>
                </div>
                {selectedFile && (
                  <div className='text-sm text-green-600'>Đã chọn file: {selectedFile.name}</div>
                )}
              </div>
            </>
          ) : (
            <ImportPreviewTable data={parsedData} />
          )}
        </div>
      </PosModal>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />

      {/* Validation Error Modal */}
      <ValidationErrorModal
        open={showValidationModal}
        onOpenChange={handleValidationModalClose}
        errors={validationErrors}
        onRetry={handleValidationRetry}
        onClose={() => {
          // Close the entire import flow when validation modal is closed
          onOpenChange(false)
        }}
        storeName={getSelectedStoreName()}
      />
    </>
  )
}
