import { ColumnDef } from '@tanstack/react-table'

import { Channel } from '@/types/channels'

import { ChannelActionsCell } from './channel-actions-cell'

export const channelsColumns: ColumnDef<Channel>[] = [
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'source_name',
    header: 'Kênh',
    cell: ({ row }) => {
      const channel = row.original
      return <span className='font-medium'>{channel.source_name}</span>
    }
  },
  {
    accessorKey: 'stores.store_name',
    header: 'C<PERSON>a hàng',
    cell: ({ row }) => {
      const channel = row.original
      return <span className='font-medium'>{channel.stores.store_name}</span>
    }
  },
  {
    accessorKey: 'extra_data.commission',
    header: 'Phần trăm hoa hồng',
    cell: ({ row }) => {
      const channel = row.original
      const commission = channel.extra_data.commission * 100 // Convert to percentage
      return <span className='font-medium'>{commission}%</span>
    }
  },
  {
    accessorKey: 'extra_data.payment_method_name',
    header: 'PTTT',
    cell: ({ row }) => {
      const channel = row.original
      const paymentType = channel.extra_data.payment_type
      const paymentMethod = channel.extra_data.payment_method_name
      return (
        <span className='font-medium'>
          {paymentType === 'POSTPAID' ? 'Trả sau' : 'Trả trước'} - {paymentMethod}
        </span>
      )
    }
  },
  {
    id: 'actions',
    header: '',
    cell: ({ row }) => {
      return <ChannelActionsCell channel={row.original} />
    },
    enableSorting: false
  }
]
