import type { ChannelFormData } from '../data'

export const useChannelValidation = (formData: ChannelFormData) => {
  const errors: Record<string, string> = {}

  // Required fields
  if (!formData.sourceName.length) {
    errors.sourceName = 'Vui lòng chọn nguồn'
  }

  if (!formData.storeId) {
    errors.storeId = 'Vui lòng chọn cửa hàng'
  }

  if (!formData.paymentMethodId) {
    errors.paymentMethodId = 'Vui lòng chọn hình thức thanh toán'
  }

  // Validation cho trường hợp chọn "Trả sau"
  if (formData.paymentMethodId === 'postpaid' && !formData.selectedPaymentMethodId) {
    errors.selectedPaymentMethodId = 'Vui lòng chọn phương thức thanh toán'
  }

  // Numeric validations
  if (formData.commissionRate < 0 || formData.commissionRate > 100) {
    errors.commissionRate = 'Tỷ lệ hoa hồng phải từ 0 đến 100%'
  }

  if (formData.deductTaxRate < 0 || formData.deductTaxRate > 100) {
    errors.deductTaxRate = 'Tỷ lệ khấu trừ thuế phải từ 0 đến 100%'
  }

  if (formData.marketingPartnerCost < 0) {
    errors.marketingPartnerCost = 'Chi phí đối tác marketing không được âm'
  }

  // Date validations
  if (formData.marketingStartDate && formData.marketingEndDate) {
    const startDate = new Date(formData.marketingStartDate)
    const endDate = new Date(formData.marketingEndDate)

    if (startDate >= endDate) {
      errors.marketingEndDate = 'Ngày kết thúc phải sau ngày bắt đầu'
    }
  }

  const validationErrors = errors
  const isValid = Object.keys(validationErrors).length === 0

  return {
    validationErrors,
    isValid
  }
}
