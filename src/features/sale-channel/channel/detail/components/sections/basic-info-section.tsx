import { ChevronDown, Search } from 'lucide-react'

import { cn } from '@/lib/utils'

import {
  Button,
  Input,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Checkbox
} from '@/components/ui'

import type { ChannelFormData } from '../../data'
import { useStoreSelection, useSourceSelection } from '../../hooks'
import type { FormMode } from '../forms/channel-form'

interface BasicInfoSectionProps {
  formData: ChannelFormData
  onFormDataChange: (updates: Partial<ChannelFormData>) => void
  mode: FormMode
  isLoading?: boolean
}

export function BasicInfoSection({
  formData,
  onFormDataChange,
  mode,
  isLoading = false
}: BasicInfoSectionProps) {
  // Store selection hook
  const storeSelection = useStoreSelection()

  // Source selection hook
  const sourceSelection = useSourceSelection(formData.storeId, formData.sourceName || [])

  const selectedStore = storeSelection.getSelectedStore(formData.storeId)
  // Display source names directly from formData
  const selectedSourceNames =
    formData.sourceName && formData.sourceName.length > 0 ? formData.sourceName.join(', ') : null

  const handleStoreSelect = (storeId: string) => {
    onFormDataChange({
      storeId,
      sourceName: [] // Reset source when store changes
    })
    sourceSelection.resetSources()
    storeSelection.handleStoreSelect(storeId, () => {})
  }

  const handleSourceDialogConfirm = () => {
    sourceSelection.handleDialogConfirm(sources => {
      onFormDataChange({ sourceName: sources })
    })
  }

  return (
    <div className='rounded-lg border p-6'>
      <h2 className='mb-6 text-xl font-semibold'>Thông tin cơ bản</h2>

      <div className='space-y-6'>
        {/* Cửa hàng */}
        <div className='flex items-center gap-4'>
          <Label className='min-w-[200px] text-sm font-medium'>
            Cửa hàng <span className='text-red-500'>*</span>
          </Label>
          <Popover
            open={storeSelection.isPopoverOpen}
            onOpenChange={storeSelection.setIsPopoverOpen}
          >
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                role='combobox'
                aria-expanded={storeSelection.isPopoverOpen}
                className='flex-1 justify-between'
                disabled={isLoading || storeSelection.isLoading || mode === 'edit'}
              >
                <span className='truncate'>
                  {selectedStore ? selectedStore.name : 'Chọn cửa hàng'}
                  {mode === 'edit' && ' (Không thể thay đổi)'}
                </span>
                <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-[var(--radix-popover-trigger-width)] p-0' align='start'>
              <div className='flex flex-col'>
                <div className='flex items-center border-b px-3 py-2'>
                  <Search className='mr-2 h-4 w-4 shrink-0 opacity-50' />
                  <Input
                    placeholder='Tìm kiếm cửa hàng...'
                    value={storeSelection.searchTerm}
                    onChange={e => storeSelection.setSearchTerm(e.target.value)}
                    className='border-0 p-0 focus-visible:ring-0'
                  />
                </div>
                <div className='max-h-60 overflow-auto'>
                  {storeSelection.isLoading ? (
                    <div className='py-6 text-center text-sm text-gray-500'>Đang tải...</div>
                  ) : storeSelection.filteredStores.length === 0 ? (
                    <div className='py-6 text-center text-sm text-gray-500'>
                      Không tìm thấy cửa hàng nào.
                    </div>
                  ) : (
                    <div className='py-1'>
                      {storeSelection.filteredStores.map(store => (
                        <div
                          key={store.id}
                          className={cn(
                            'cursor-pointer px-3 py-2 text-sm hover:bg-gray-100',
                            formData.storeId === store.id && 'bg-gray-100'
                          )}
                          onClick={() => handleStoreSelect(store.id)}
                        >
                          {store.name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Nguồn đơn */}
        <div className='flex items-center gap-4'>
          <Label className='min-w-[200px] text-sm font-medium'>
            Nguồn <span className='text-red-500'>*</span>
          </Label>
          <Dialog
            open={sourceSelection.isDialogOpen}
            onOpenChange={sourceSelection.setIsDialogOpen}
          >
            <DialogTrigger asChild>
              <Button
                variant='outline'
                className='flex-1 justify-between'
                disabled={
                  isLoading || sourceSelection.isLoading || !formData.storeId || mode === 'edit'
                }
              >
                <span className='truncate'>
                  {selectedSourceNames || 'Chọn nguồn'}
                  {mode === 'edit' && ' (Không thể thay đổi)'}
                </span>
              </Button>
            </DialogTrigger>
            <DialogContent className='max-w-md'>
              <DialogHeader>
                <DialogTitle>Chọn nguồn</DialogTitle>
              </DialogHeader>
              <div className='space-y-4'>
                {/* Search input */}
                <Input
                  placeholder='Tìm kiếm'
                  value={sourceSelection.searchTerm}
                  onChange={e => sourceSelection.setSearchTerm(e.target.value)}
                />

                {/* Selected sources section - Always show */}
                {formData.storeId && !sourceSelection.isLoading && (
                  <div className='space-y-2'>
                    <div
                      className='flex cursor-pointer items-center justify-between text-sm text-green-600'
                      onClick={() =>
                        sourceSelection.setShowSelectedSources(!sourceSelection.showSelectedSources)
                      }
                    >
                      <span>✓ Đã chọn {sourceSelection.selectedSources.length}</span>
                      <ChevronDown
                        className={cn(
                          'h-4 w-4 transition-transform',
                          !sourceSelection.showSelectedSources && 'rotate-180'
                        )}
                      />
                    </div>

                    {sourceSelection.showSelectedSources &&
                      sourceSelection.selectedSourcesData.length > 0 && (
                        <div className='space-y-2 border-l-2 border-green-200 pl-4'>
                          {sourceSelection.selectedSourcesData.map(source => (
                            <div key={source.id} className='flex items-center space-x-2'>
                              <Checkbox
                                id={`selected-${source.id}`}
                                checked={true}
                                onCheckedChange={() =>
                                  sourceSelection.handleSourceToggle(source.id)
                                }
                              />
                              <label
                                htmlFor={`selected-${source.id}`}
                                className='cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                              >
                                {source.source_name}
                              </label>
                            </div>
                          ))}
                        </div>
                      )}
                  </div>
                )}

                {/* Remaining sources section - Always show */}
                {formData.storeId && !sourceSelection.isLoading && (
                  <div className='space-y-2'>
                    <div className='text-sm text-gray-500'>
                      Còn lại {sourceSelection.unselectedSourcesData.length}
                    </div>

                    {sourceSelection.unselectedSourcesData.length > 0 && (
                      <div className='max-h-40 space-y-2 overflow-auto'>
                        {sourceSelection.unselectedSourcesData.map(source => (
                          <div key={source.id} className='flex items-center space-x-2'>
                            <Checkbox
                              id={`unselected-${source.id}`}
                              checked={false}
                              onCheckedChange={() => sourceSelection.handleSourceToggle(source.id)}
                            />
                            <label
                              htmlFor={`unselected-${source.id}`}
                              className='cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                            >
                              {source.source_name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Loading and empty states */}
                {!formData.storeId ? (
                  <div className='py-6 text-center text-sm text-gray-500'>
                    Vui lòng chọn cửa hàng trước.
                  </div>
                ) : sourceSelection.isLoading ? (
                  <div className='py-6 text-center text-sm text-gray-500'>Đang tải...</div>
                ) : sourceSelection.filteredSources.length === 0 ? (
                  <div className='text-center text-sm text-gray-400'>Không có dữ liệu</div>
                ) : null}

                {/* Action buttons */}
                <div className='flex justify-between space-x-2 pt-4'>
                  <Button variant='outline' onClick={sourceSelection.handleDialogCancel}>
                    Hủy
                  </Button>
                  <Button onClick={handleSourceDialogConfirm}>Xong</Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  )
}
