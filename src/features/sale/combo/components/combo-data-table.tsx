import { useState } from 'react'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  RowSelectionState,
  getPaginationRowModel
} from '@tanstack/react-table'

import { Trash2 } from 'lucide-react'

import type { Combo } from '@/lib/combos-api'

import { ConfirmModal } from '@/components/pos'
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'

interface ComboDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
}

export function ComboDataTable<TData, TValue>({
  columns,
  data
}: ComboDataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10
      }
    }
  })

  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedCount = selectedRows.length

  const handleDeleteClick = () => {
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    const comboIds = selectedRows.map(row => {
      const combo = row.original as Combo
      return combo.id
    })

    try {
      // TODO: Implement bulk delete functionality
      console.log('Delete combos:', comboIds)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Clear selection after delete
      setRowSelection({})
      setConfirmModalOpen(false)
    } catch (error) {
      console.error('Error deleting combos:', error)
    }
  }

  const handleCancelDelete = () => {
    setConfirmModalOpen(false)
  }

  return (
    <div className='space-y-4'>
      {/* Delete Button - Show when rows are selected */}
      {selectedCount > 0 && (
        <div className='bg-muted/50 flex items-center justify-start rounded-md border p-3'>
          <Button variant='destructive' size='sm' onClick={handleDeleteClick} className='h-8'>
            <Trash2 className='mr-2 h-4 w-4' />
            Xóa combo
          </Button>
        </div>
      )}

      {/* Data Table */}
      <div className='rounded-md'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} style={{ width: header.getSize() }}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className='hover:bg-gray-50'
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} style={{ width: cell.column.getSize() }}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  <div className='flex flex-col items-center justify-center space-y-2'>
                    <p className='text-muted-foreground'>Không có combo nào</p>
                    <p className='text-muted-foreground text-sm'>
                      Hãy tạo combo mới để bắt đầu quản lý
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className='flex items-center justify-between space-x-2 py-4'>
        <div className='text-muted-foreground text-sm'>
          {selectedCount > 0 && (
            <span>
              Đã chọn {selectedCount} trong {table.getFilteredRowModel().rows.length} combo
            </span>
          )}
        </div>
        <div className='flex items-center space-x-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Trước
          </Button>
          <Button
            variant='outline'
            size='sm'
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Sau
          </Button>
        </div>
      </div>

      {/* Confirm Delete Modal */}
      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        title='Lưu ý: Xóa combo có thể ảnh hưởng đến dữ liệu đang vận hành tại POS'
        content={`Bạn có muốn xoá ${selectedCount} combo đã chọn ?`}
        confirmText='Xóa'
        cancelText='Hủy'
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  )
}
