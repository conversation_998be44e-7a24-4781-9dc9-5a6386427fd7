import { useState } from 'react'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  RowSelectionState
} from '@tanstack/react-table'

import { Trash2 } from 'lucide-react'

import type { Combo } from '@/lib/combos-api'

import { useDeleteCombos } from '@/hooks/api/use-combos'

import { ConfirmModal } from '@/components/pos'
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui'

interface ComboDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
}

export function ComboDataTable<TData, TValue>({
  columns,
  data
}: ComboDataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)

  const { mutate: deleteCombos, isPending: isDeleting } = useDeleteCombos()

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel()
  })

  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedCount = selectedRows.length

  const handleDeleteClick = () => {
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = () => {
    const packageUids = selectedRows.map(row => {
      const combo = row.original as Combo
      return combo.id
    })

    deleteCombos(
      { packageUids },
      {
        onSuccess: () => {
          // Clear selection after delete
          setRowSelection({})
          setConfirmModalOpen(false)
        }
      }
    )
  }

  const handleCancelDelete = () => {
    setConfirmModalOpen(false)
  }

  return (
    <div className='space-y-4'>
      {/* Delete Button - Show when rows are selected */}
      {selectedCount > 0 && (
        <div className='bg-muted/50 flex items-center justify-start rounded-md border p-3'>
          <Button
            variant='destructive'
            size='sm'
            onClick={handleDeleteClick}
            className='h-8'
            disabled={isDeleting}
          >
            <Trash2 className='mr-2 h-4 w-4' />
            {isDeleting ? 'Đang xóa...' : 'Xóa combo'}
          </Button>
        </div>
      )}

      {/* Data Table */}
      <div className='rounded-md'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} style={{ width: header.getSize() }}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className='hover:bg-gray-50'
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} style={{ width: cell.column.getSize() }}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  <div className='flex flex-col items-center justify-center space-y-2'>
                    <p className='text-muted-foreground'>Không có combo nào</p>
                    <p className='text-muted-foreground text-sm'>
                      Hãy tạo combo mới để bắt đầu quản lý
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Selection Info */}
      {selectedCount > 0 && (
        <div className='flex items-center justify-between space-x-2 py-4'>
          <div className='text-muted-foreground text-sm'>
            <span>
              Đã chọn {selectedCount} trong {table.getFilteredRowModel().rows.length} combo
            </span>
          </div>
        </div>
      )}

      {/* Confirm Delete Modal */}
      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        title='Lưu ý: Xóa combo có thể ảnh hưởng đến dữ liệu đang vận hành tại POS'
        content={`Bạn có muốn xoá ${selectedCount} combo đã chọn ?`}
        confirmText='Xóa'
        cancelText='Hủy'
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  )
}
