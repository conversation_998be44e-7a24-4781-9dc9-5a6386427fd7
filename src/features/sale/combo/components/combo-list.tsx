import { useState, useEffect } from 'react'

import { Search, Settings, Plus, Download, Upload, ArrowUpDown } from 'lucide-react'

import { useCombosData, useComboPromotionsData } from '@/hooks/api/use-combos'
import { useStoresData } from '@/hooks/api/use-stores'

import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui'

import { createComboColumns } from './combo-columns'
import { ComboDataTable } from './combo-data-table'

export function ComboList() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStore, setSelectedStore] = useState<string>('all')
  const [selectedPromotion, setSelectedPromotion] = useState<string>('all')
  const [expiryStatus, setExpiryStatus] = useState<'all' | 'unexpired' | 'expired'>('all')

  // Handle search when Enter is pressed
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setSearchQuery(searchTerm)
    }
  }

  // Fetch data
  const { data: stores = [] } = useStoresData()
  const { data: promotions = [] } = useComboPromotionsData({
    storeUid: selectedStore === 'all' ? undefined : selectedStore
  })

  const { data: combos = [], isLoading: combosLoading } = useCombosData({
    storeUid: selectedStore === 'all' ? stores.map(store => store.id) : selectedStore,
    status: expiryStatus,
    search: searchQuery || undefined,
    promotionUid: selectedPromotion === 'all' ? undefined : selectedPromotion
  })

  const handleCreateCombo = () => {
    // TODO: Navigate to create combo page
    console.log('Create new combo')
  }

  const handleExportCombo = () => {
    // TODO: Export combo functionality
    console.log('Export combo')
  }

  const handleImportCombo = () => {
    // TODO: Import combo from file
    console.log('Import combo from file')
  }

  const handleSortCombo = () => {
    // TODO: Sort combo functionality
    console.log('Sort combo')
  }

  const columns = createComboColumns()

  return (
    <div className='space-y-6'>
      {/* Header and Filters Row */}
      <div className='space-y-4'>
        <div className='flex flex-wrap items-center gap-4'>
          {/* Title */}
          <h1 className='text-2xl font-semibold'>Combo</h1>
          {/* Search Input */}
          <div className='relative max-w-[300px] min-w-[200px] flex-1'>
            <Search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2' />
            <Input
              placeholder='Tìm kiếm combo (nhấn Enter để tìm)'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              onKeyDown={handleSearchKeyDown}
              className='pl-9'
            />
          </div>

          {/* Store Dropdown */}
          <Select value={selectedStore} onValueChange={setSelectedStore}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Chọn cửa hàng' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả cửa hàng</SelectItem>
              {stores.map(store => (
                <SelectItem key={store.id} value={store.id}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Promotion Dropdown */}
          <Select value={selectedPromotion} onValueChange={setSelectedPromotion}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Chọn khuyến mãi' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả khuyến mãi</SelectItem>
              {promotions.map(promotion => (
                <SelectItem key={promotion.promotion_id} value={promotion.promotion_id}>
                  {promotion.promotion_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Expiry Status Dropdown */}
          <Select
            value={expiryStatus}
            onValueChange={(value: 'all' | 'unexpired' | 'expired') => setExpiryStatus(value)}
          >
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Trạng thái' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả ngày áp dụng</SelectItem>
              <SelectItem value='expired'>Hết hạn</SelectItem>
              <SelectItem value='unexpired'>Chưa hết hạn</SelectItem>
            </SelectContent>
          </Select>

          {/* Action Buttons */}
          <div className='ml-auto flex items-center gap-2'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' className='flex items-center gap-2'>
                  <Settings className='h-4 w-4' />
                  Tiện ích
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={handleExportCombo}>
                  <Download className='mr-2 h-4 w-4' />
                  Xuất, sửa combo
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleImportCombo}>
                  <Upload className='mr-2 h-4 w-4' />
                  Thêm combo từ file
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleSortCombo}>
                  <ArrowUpDown className='mr-2 h-4 w-4' />
                  Sắp xếp combo
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button onClick={handleCreateCombo} className='flex items-center gap-2'>
              <Plus className='h-4 w-4' />
              Tạo combo
            </Button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className='bg-white'>
        {combosLoading ? (
          <div className='flex h-64 items-center justify-center'>
            <div className='text-center'>
              <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2'></div>
              <p className='text-muted-foreground'>Đang tải dữ liệu combo...</p>
            </div>
          </div>
        ) : (
          <ComboDataTable columns={columns} data={combos} />
        )}
      </div>
    </div>
  )
}
