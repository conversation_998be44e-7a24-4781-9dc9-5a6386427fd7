import React, { useState } from 'react'

import { ColumnDef } from '@tanstack/react-table'

import { Copy, Trash2 } from 'lucide-react'

import type { Combo } from '@/lib/combos-api'

import { useDeleteCombos } from '@/hooks/api/use-combos'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'

import { ConfirmModal } from '@/components/pos'

import { CopyComboModal } from './copy-combo-modal'

// Helper function to format currency
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(value)
}

// Helper function to format date range or show expired badge
function formatDateRange(fromDate: number, toDate: number): JSX.Element {
  const currentTime = Date.now()

  // Check if combo is expired (to_date is in the past)
  if (toDate < currentTime) {
    return (
      <Badge variant='destructive' className='text-xs'>
        <PERSON><PERSON><PERSON> hạn
      </Badge>
    )
  }

  const from = new Date(fromDate).toLocaleDateString('vi-VN')
  const to = new Date(toDate).toLocaleDateString('vi-VN')
  return <span>{`${from} - ${to}`}</span>
}

// Helper function to get store names
function getStoreNames(combo: Combo): string {
  // If combo has store info, use it; otherwise show store count
  if (combo.stores === 1) {
    return '1 cửa hàng'
  }
  return `${combo.stores} cửa hàng`
}

function CopyCell({ combo }: { combo: Combo }) {
  const [copyModalOpen, setCopyModalOpen] = useState(false)

  return (
    <>
      <Button
        variant='ghost'
        size='sm'
        onClick={() => setCopyModalOpen(true)}
        className='h-8 w-8 p-0'
      >
        <Copy className='h-4 w-4' />
      </Button>

      <CopyComboModal open={copyModalOpen} onOpenChange={setCopyModalOpen} combo={combo} />
    </>
  )
}

function DeleteCell({ combo }: { combo: Combo }) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const { mutate: deleteCombos, isPending: isDeleting } = useDeleteCombos()

  const handleDelete = () => {
    deleteCombos(
      { packageUids: [combo.id] },
      {
        onSuccess: () => {
          setDeleteDialogOpen(false)
        }
      }
    )
  }

  return (
    <>
      <Button
        variant='ghost'
        size='sm'
        onClick={() => setDeleteDialogOpen(true)}
        className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
      >
        <Trash2 className='h-4 w-4' />
      </Button>

      <ConfirmModal
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Xác nhận xóa combo'
        content={`Bạn có chắc chắn muốn xóa combo "${combo.package_name}"? Hành động này không thể hoàn tác.`}
        confirmText='Xóa'
        cancelText='Hủy'
        onConfirm={handleDelete}
        isLoading={isDeleting}
      />
    </>
  )
}

export const createComboColumns = (): ColumnDef<Combo>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    accessorKey: 'index',
    header: '#',
    cell: ({ row, table }) => {
      const pageIndex = table.getState().pagination.pageIndex
      const pageSize = table.getState().pagination.pageSize
      return pageIndex * pageSize + row.index + 1
    },
    size: 60
  },
  {
    accessorKey: 'package_name',
    header: 'Tên combo',
    cell: ({ row }) => {
      const combo = row.original
      return <div className='font-medium'>{combo.package_name}</div>
    },
    size: 200
  },
  {
    accessorKey: 'ots_value',
    header: 'Giá',
    cell: ({ row }) => {
      const combo = row.original
      return <div className='font-mono'>{formatCurrency(combo.ots_value)}</div>
    },
    size: 120
  },
  {
    accessorKey: 'stores',
    header: 'Cửa hàng',
    cell: ({ row }) => {
      const combo = row.original
      return <div className='text-sm'>{getStoreNames(combo)}</div>
    },
    size: 120
  },
  {
    accessorKey: 'date_range',
    header: 'Thời gian áp dụng',
    cell: ({ row }) => {
      const combo = row.original
      return <div className='text-sm'>{formatDateRange(combo.from_date, combo.to_date)}</div>
    },
    size: 150
  },
  {
    id: 'copy',
    header: 'Sao chép',
    cell: ({ row }) => <CopyCell combo={row.original} />,
    enableSorting: false,
    size: 80
  },
  {
    id: 'delete',
    header: '',
    cell: ({ row }) => <DeleteCell combo={row.original} />,
    enableSorting: false,
    size: 60
  }
]
