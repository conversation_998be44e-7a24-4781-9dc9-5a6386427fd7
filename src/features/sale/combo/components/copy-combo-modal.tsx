import { useState } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import type { Combo } from '@/lib/combos-api'

import { useGetComboDetails, useCopyCombos } from '@/hooks/api/use-combos'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input
} from '@/components/ui'

const copyComboSchema = z.object({
  comboName: z.string().min(1, 'Tên combo là bắt buộc')
})

type CopyComboFormData = z.infer<typeof copyComboSchema>

/**
 * Generate a random combo ID in the format COMBO-XXXX
 */
const generateComboId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return `COMBO-${result}`
}

interface CopyComboModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  combo: Combo
}

export function CopyComboModal({ open, onOpenChange, combo }: CopyComboModalProps) {
  const [isLoading, setIsLoading] = useState(false)

  const { mutateAsync: getComboDetails } = useGetComboDetails()
  const { mutateAsync: copyCombos } = useCopyCombos()

  const form = useForm<CopyComboFormData>({
    resolver: zodResolver(copyComboSchema),
    defaultValues: {
      comboName: `${combo.package_name} - Copy`
    }
  })

  const handleCopy = async (data: CopyComboFormData) => {
    setIsLoading(true)
    try {
      // 1. Get combo details
      const response = await getComboDetails({ packageUids: [combo.id] })
      const comboDetails = response.data[0]

      if (!comboDetails) {
        throw new Error('Không tìm thấy thông tin combo')
      }

      // 2. Prepare combo data for copying
      const currentTime = Math.floor(Date.now() / 1000) // Unix timestamp in seconds
      const newPackageId = generateComboId() // Generate new combo ID

      const newCombo = {
        ...comboDetails,
        package_name: data.comboName,
        package_id: newPackageId, // Use generated combo ID
        created_at: currentTime,
        updated_at: currentTime,
        // Remove fields that should not be copied
        id: undefined
      }

      // Remove undefined fields
      const cleanCombo = Object.fromEntries(
        Object.entries(newCombo).filter(([_, value]) => value !== undefined)
      )

      // 3. Create new combo
      await copyCombos({ combos: [cleanCombo as any] })

      // 4. Close modal and reset form
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error('Error copying combo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    form.reset()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>Sao chép {combo.package_name}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleCopy)} className='space-y-4'>
            <FormField
              control={form.control}
              name='comboName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên combo *</FormLabel>
                  <FormControl>
                    <Input placeholder='Nhập tên combo' {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className='gap-2'>
              <Button type='button' variant='outline' onClick={handleCancel} disabled={isLoading}>
                Hủy
              </Button>
              <Button type='submit' disabled={isLoading || !form.formState.isValid}>
                {isLoading ? 'Đang sao chép...' : 'Sao chép'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
