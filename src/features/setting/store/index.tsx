import { getErrorMessage } from '@/utils/error-utils'

import { storesColumns, StoresDataTable, StoreHeader } from './components'
import { useStoreList } from './hooks'

export function StoreList() {
  const {
    filteredStores,
    cities,
    isLoading,
    citiesLoading,
    error,
    searchTerm,
    setSearchTerm,
    selectedCity,
    setSelectedCity,
    handleCreateStore,
    handleImportStores
  } = useStoreList()

  return (
    <div className='container mx-auto px-4 py-8'>
      <StoreHeader
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        selectedCity={selectedCity}
        onCityChange={setSelectedCity}
        cities={cities}
        citiesLoading={citiesLoading}
        onCreateStore={handleCreateStore}
        onImportStores={handleImportStores}
      />

      {error ? (
        <div className='py-8 text-center'>
          <p className='text-red-600'>{getErrorMessage(error)}</p>
        </div>
      ) : isLoading ? (
        <div className='py-8 text-center'>
          <p><PERSON><PERSON> tải dữ liệu cửa hàng...</p>
        </div>
      ) : (
        <StoresDataTable columns={storesColumns} data={filteredStores} />
      )}
    </div>
  )
}
