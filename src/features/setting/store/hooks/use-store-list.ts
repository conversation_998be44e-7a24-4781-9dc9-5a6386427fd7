import { useCitiesData } from '@/hooks/api/use-removed-items'
import { useStoresData } from '@/hooks/api/use-stores'

import { useStoreListState } from './use-store-list-state'
import { useCitiesTransform } from './use-cities-transform'
import { useStoreFiltering } from './use-store-filtering'

export interface UseStoreListReturn {
  // Data
  stores: any[]
  filteredStores: any[]
  cities: Array<{ id: string; name: string }>
  
  // Loading states
  isLoading: boolean
  citiesLoading: boolean
  
  // Error state
  error: any
  
  // State management
  searchTerm: string
  setSearchTerm: (term: string) => void
  selectedCity: string
  setSelectedCity: (city: string) => void
  
  // Actions
  handleCreateStore: () => void
  handleImportStores: () => void
}

/**
 * Main hook that combines all store list functionality
 */
export function useStoreList(): UseStoreListReturn {
  // State management
  const { searchTerm, setSearchTerm, selectedCity, setSelectedCity } = useStoreListState()
  
  // Data fetching
  const { data: stores = [], isLoading, error } = useStoresData()
  const { data: citiesData = [], isLoading: citiesLoading } = useCitiesData()
  
  // Data transformation
  const { cities } = useCitiesTransform(citiesData)
  
  // Filtering
  const { filteredStores } = useStoreFiltering({
    stores,
    searchTerm,
    selectedCity,
  })
  
  // Action handlers
  const handleCreateStore = () => {
    // TODO: Navigate to create store page when route is available
    // navigate({ to: '/setting/store/create' })
  }

  const handleImportStores = () => {
    // TODO: Implement import stores functionality
  }

  return {
    // Data
    stores,
    filteredStores,
    cities,
    
    // Loading states
    isLoading,
    citiesLoading,
    
    // Error state
    error,
    
    // State management
    searchTerm,
    setSearchTerm,
    selectedCity,
    setSelectedCity,
    
    // Actions
    handleCreateStore,
    handleImportStores,
  }
}
