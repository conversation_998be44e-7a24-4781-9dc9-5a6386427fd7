import type { CityData } from '@/types/item-removed'

export interface TransformedCity {
  id: string
  name: string
}

export interface UseCitiesTransformReturn {
  cities: TransformedCity[]
}

/**
 * Custom hook for transforming cities data to match UI interface
 */
export function useCitiesTransform(citiesData: CityData[] = []): UseCitiesTransformReturn {
  const cities = citiesData.map(city => ({
    id: (city as CityData & { city_id?: string }).city_id || city.id,
    name: city.city_name
  }))

  return {
    cities
  }
}
