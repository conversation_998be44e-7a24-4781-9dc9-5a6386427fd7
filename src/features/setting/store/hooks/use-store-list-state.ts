import { useState } from 'react'

export interface UseStoreListStateReturn {
  searchTerm: string
  setSearchTerm: (term: string) => void
  selectedCity: string
  setSelectedCity: (city: string) => void
}

/**
 * Custom hook for managing store list state (search and filter)
 */
export function useStoreListState(): UseStoreListStateReturn {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCity, setSelectedCity] = useState('all')

  return {
    searchTerm,
    setSearchTerm,
    selectedCity,
    setSelectedCity,
  }
}
