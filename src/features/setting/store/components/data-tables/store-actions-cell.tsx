import { Store } from '@/types/store'

import { DeleteStoreButton } from './delete-store-button'

interface StoreActionsCellProps {
  store: Store
}

export function StoreActionsCell({ store }: StoreActionsCellProps) {
  const handleCellClick = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent row click when clicking in actions cell
  }

  return (
    <div className='flex items-center justify-end' onClick={handleCellClick}>
      <DeleteStoreButton store={store} />
    </div>
  )
}
