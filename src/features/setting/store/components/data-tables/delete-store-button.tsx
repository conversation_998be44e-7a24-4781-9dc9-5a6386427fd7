import { useState } from 'react'

import { IconTrash } from '@tabler/icons-react'
import { toast } from 'sonner'

import { Store } from '@/types'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button
} from '@/components/ui'

interface DeleteStoreButtonProps {
  store: Store
}

export function DeleteStoreButton({ store }: DeleteStoreButtonProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    try {
      setIsDeleting(true)
      // TODO: Implement delete store API call
      // await deleteStore(store.id)
      toast.success(`Đã xóa cửa hàng ${store.name}`)
      setIsDeleteDialogOpen(false)
    } catch (_error) {
      toast.error('Có lỗi xảy ra khi xóa cửa hàng')
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <>
      <Button
        variant='ghost'
        size='sm'
        onClick={() => setIsDeleteDialogOpen(true)}
        className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
      >
        <IconTrash className='h-4 w-4' />
      </Button>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa cửa hàng</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa cửa hàng "{store.name}"? Hành động này không thể hoàn tác.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button
              variant='destructive'
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xóa'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
