import { ColumnDef } from '@tanstack/react-table'

import { Store } from '@/types'
import { Info, Home, Edit } from 'lucide-react'

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui'

export const storesColumns: ColumnDef<Store>[] = [
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'fb_store_id',
    header: 'Pos ID',
    cell: ({ row }) => {
      const store = row.original
      // Use fb_store_id from the API data, fallback to code if not available
      const posId = (store as any).fb_store_id || store.code
      return <span className='font-medium'>{posId}</span>
    }
  },
  {
    accessorKey: 'name',
    header: 'Tên',
    cell: ({ row }) => {
      const store = row.original
      return <span className='font-medium'>{store.name}</span>
    }
  },
  {
    accessorKey: 'city_name',
    header: 'Đ<PERSON><PERSON> điểm',
    cell: ({ row }) => {
      const store = row.original as any
      const cityName = store.city_name || store.cityName
      const address = store.address
      const latitude = store.latitude
      const longitude = store.longitude

      return (
        <div className='flex items-center gap-2'>
          <span className='font-medium'>{cityName}</span>
          {address && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className='text-muted-foreground hover:text-foreground h-4 w-4 cursor-help transition-colors' />
                </TooltipTrigger>
                <TooltipContent className='max-w-xs'>
                  <div className='space-y-1'>
                    <p className='text-sm'>{address}</p>
                    {latitude && longitude && (
                      <p className='text-xs'>
                        lat: {latitude}, long: {longitude}
                      </p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      )
    }
  },
  {
    accessorKey: 'phone',
    header: 'Số điện thoại',
    cell: ({ row }) => {
      const store = row.original
      return store.phone ? (
        <span className='font-medium'>{store.phone}</span>
      ) : (
        <span className='text-muted-foreground'>-</span>
      )
    }
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      const store = row.original
      return store.email ? (
        <span className='font-medium'>{store.email}</span>
      ) : (
        <span className='text-muted-foreground'>-</span>
      )
    }
  },
  {
    accessorKey: 'expiry_date',
    header: 'Thời hạn bán quyền',
    cell: ({ row }) => {
      const store = row.original as any
      const expiryTimestamp = store.expiry_date || store.expiryDate

      if (!expiryTimestamp) {
        return <span className='text-muted-foreground'>-</span>
      }

      // Convert timestamp to date
      const expiryDate = new Date(expiryTimestamp * 1000)
      const isExpired = expiryDate < new Date()

      return isExpired ? (
        <span className='rounded bg-red-100 px-2 py-1 text-xs text-red-600'>Hết hạn bán quyền</span>
      ) : (
        <span className='font-medium'>{expiryDate.toLocaleDateString('vi-VN')}</span>
      )
    }
  },
  {
    accessorKey: 'store_type',
    header: 'Loại hình nhà hàng',
    cell: () => {
      return (
        <div className='flex w-fit items-center justify-center gap-2 rounded bg-blue-500 px-2 py-1 text-white'>
          <Home className='h-4 w-4' />
          <Edit className='h-4 w-4' />
        </div>
      )
    }
  },
  {
    accessorKey: 'active',
    header: '',
    cell: ({ row }) => {
      const store = row.original as any
      const isActive = store.active === 1 || store.status === 'active'

      return (
        <span
          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
            isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
          }`}
        >
          {isActive ? 'Active' : 'Inactive'}
        </span>
      )
    }
  }
]
