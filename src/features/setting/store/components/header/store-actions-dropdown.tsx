import { IconSettings } from '@tabler/icons-react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Button
} from '@/components/ui'

interface StoreActionsDropdownProps {
  onImportStores: () => void
}

export function StoreActionsDropdown({ onImportStores }: StoreActionsDropdownProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='sm'>
          Tiện ích
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem onClick={onImportStores}>
          <IconSettings className='mr-2 h-4 w-4' />
          Thêm nhà hàng từ file
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
