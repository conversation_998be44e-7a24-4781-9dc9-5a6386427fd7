import { IconPlus } from '@tabler/icons-react'

import { Button, Input } from '@/components/ui'

import { CitySelect } from './city-select'
import { StoreActionsDropdown } from './store-actions-dropdown'

interface City {
  id: string
  name: string
}

interface StoreHeaderProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  selectedCity: string
  onCityChange: (value: string) => void
  cities?: City[]
  citiesLoading?: boolean
  onCreateStore: () => void
  onImportStores: () => void
}

export function StoreHeader({
  searchTerm,
  onSearchChange,
  selectedCity,
  onCityChange,
  cities,
  citiesLoading,
  onCreateStore,
  onImportStores
}: StoreHeaderProps) {
  return (
    <div className='mb-4 space-y-4'>
      {/* Title and Actions */}
      <div className='flex items-center justify-between'>
        <h2 className='text-xl font-semibold'>Danh sách nhà hàng</h2>

        <div className='flex items-center gap-2'>
          <StoreActionsDropdown onImportStores={onImportStores} />
          <Button onClick={onCreateStore}>
            <IconPlus className='mr-2 h-4 w-4' />
            Tạo nhà hàng mới
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className='flex items-center gap-4'>
        <Input
          placeholder='Tìm kiếm nhà hàng'
          value={searchTerm}
          onChange={e => onSearchChange(e.target.value)}
          className='max-w-sm'
        />

        <CitySelect
          value={selectedCity}
          onValueChange={onCityChange}
          cities={cities}
          isLoading={citiesLoading}
        />
      </div>
    </div>
  )
}
