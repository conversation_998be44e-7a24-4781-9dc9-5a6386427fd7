import { useState, useMemo, useRef } from 'react'
import { toast } from 'sonner'

import { IconUpload } from '@tabler/icons-react'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { PosModal } from '@/components/pos'
import { Button } from '@/components/ui'

import { useTablesExcelParser } from '../hooks/use-tables-excel-parser'
import { useBulkImportTables } from '@/hooks/api/use-tables'

interface ParsedTableData {
  area_name: string
  description?: string
  sort?: number
  active?: number
}

interface Store {
  id: string
  store_name: string
  active: number
}

interface ImportTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onSuccess: () => void
}

export function ImportTablesModal({
  open,
  onOpenChange,
  onCancel,
  onSuccess
}: ImportTablesModalProps) {
  // Import state
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedTableData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)
  
  // Store selection state for Step 2
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  
  // Hooks
  const { parseExcelFile } = useTablesExcelParser()
  const bulkImportTablesMutation = useBulkImportTables()

  // Get stores from localStorage (same as TablesList component)
  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  // Set default store when stores are loaded
  useMemo(() => {
    if (stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [stores, selectedStoreId])

  // Reset import state when modal closes
  useMemo(() => {
    if (!open) {
      setShowImportParsedData(false)
      setImportParsedData([])
      setImportSelectedFile(null)
      setSelectedStoreId(stores[0]?.id || '')
    }
  }, [open, stores])

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedTables = await parseExcelFile(file)
      setImportParsedData(parsedTables)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedTables.length} bàn từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleConfirm = async () => {
    if (showImportParsedData && selectedStoreId) {
      if (importParsedData.length === 0) {
        toast.error('Không có dữ liệu để lưu')
        return
      }

      try {
        await bulkImportTablesMutation.mutateAsync({
          storeUid: selectedStoreId,
          tables: importParsedData
        })
        toast.success(`Đã tạo thành công ${importParsedData.length} bàn!`)
        onSuccess()
      } catch {
        toast.error('Lỗi khi tạo bàn. Vui lòng thử lại.')
      }
    }
  }

  return (
    <PosModal
      title='Thêm bàn từ file'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={handleConfirm}
      confirmText={showImportParsedData ? 'Lưu' : 'Tiếp tục'}
      cancelText='Đóng'
      centerTitle={true}
      maxWidth={showImportParsedData ? 'sm:max-w-4xl' : 'sm:max-w-[400px]'}
      isLoading={bulkImportTablesMutation.isPending}
      hideButtons={!showImportParsedData}
      confirmDisabled={showImportParsedData && !selectedStoreId}
    >
      <div className='space-y-4'>
        {!showImportParsedData && (
          <>
            <div className='mb-4 text-sm text-gray-600'>
              Dữ liệu tải lên được áp dụng vào 1 cửa hàng cụ thể.
            </div>

            <div className='mb-4 text-sm text-gray-600'>File tải lên có cấu trúc như sau:</div>

            <div className='mb-4 text-center'>
              <img
                src='/images/setting/area/add-area.png'
                alt='File structure example'
                className='mx-auto h-auto max-w-full rounded-lg border'
              />
            </div>

            <div className='mb-4 text-sm text-gray-600'>
              Hoặc xem trong{' '}
              <a
                href='/files/setting/area/import_area_template.xlsx'
                download='import_area_template.xlsx'
                className='cursor-pointer text-blue-600 underline hover:text-blue-800'
              >
                file mẫu
              </a>
              .
            </div>

            <div className='text-center'>
              <Button
                size='default'
                variant='default'
                onClick={handleImportFileUpload}
                className='mx-auto flex items-center gap-2'
              >
                <IconUpload className='h-4 w-4' />
                Tải file lên
              </Button>
              {importSelectedFile && (
                <div className='mt-2 text-xs text-gray-500'>
                  File đã chọn: {importSelectedFile.name}
                </div>
              )}
            </div>
          </>
        )}

        {showImportParsedData && (
          <div className='space-y-4'>
            {/* Step 2: Centered Red Text */}
            <div className='text-center text-red-500'>Chọn cửa hàng áp dụng</div>

            {/* Step 2: Header Section with Store Selection */}
            <div className='flex items-center justify-between'>
              <div className='text-sm text-gray-600'>
                Các bàn sẽ áp dụng cho cửa hàng được chọn
              </div>
              <Select value={selectedStoreId} onValueChange={setSelectedStoreId}>
                <SelectTrigger className='w-[200px]'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {stores.map(store => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.store_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Step 2: Data Preview Table */}
            <div className='max-h-96 overflow-y-auto rounded-lg border'>
              <table className='w-full text-sm'>
                <thead className='sticky top-0 bg-gray-50'>
                  <tr>
                    <th className='px-4 py-2 text-left'>Tên bàn</th>
                    <th className='px-4 py-2 text-left'>Mô tả</th>
                  </tr>
                </thead>
                <tbody>
                  {importParsedData.map((area, index) => (
                    <tr key={index} className='border-t'>
                      <td className='px-4 py-2'>{area.area_name}</td>
                      <td className='px-4 py-2'>{area.description || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
      
      {/* Hidden file input */}
      <input
        ref={importFileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={handleImportFileChange}
        className='hidden'
      />
    </PosModal>
  )
}
