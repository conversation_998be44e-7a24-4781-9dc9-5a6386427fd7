import { Checkbox } from '@/components/ui/checkbox'
import { TableHead, TableHeader, TableRow } from '@/components/ui/table'

interface TablesTableHeaderProps {
  selectedCount: number
  totalCount: number
  onSelectAll: (checked: boolean) => void
}

export function TablesTableHeader({
  selectedCount,
  totalCount,
  onSelectAll
}: TablesTableHeaderProps) {
  const isAllSelected = selectedCount === totalCount && totalCount > 0
  const isIndeterminate = selectedCount > 0 && selectedCount < totalCount

  return (
    <TableHeader>
      <TableRow>
        <TableHead className='w-[50px]'>
          <Checkbox
            checked={isAllSelected}
            ref={el => {
              if (el) (el as any).indeterminate = isIndeterminate
            }}
            onCheckedChange={onSelectAll}
          />
        </TableHead>
        <TableHead className='w-[60px] text-center'>#</TableHead>
        <TableHead className='min-w-[200px]'>Tên bàn</TableHead>
        <TableHead className='min-w-[150px]'>Mã bàn</TableHead>
        <TableHead className='min-w-[120px] text-center'>Điểm áp dụng</TableHead>
        <TableHead className='min-w-[120px] text-center'>Thêm bàn</TableHead>
        <TableHead className='w-[100px] text-center'>Thao tác</TableHead>
      </TableRow>
    </TableHeader>
  )
}
