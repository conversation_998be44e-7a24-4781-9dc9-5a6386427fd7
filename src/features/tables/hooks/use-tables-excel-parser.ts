import { toast } from 'sonner'

interface ParsedTableData {
  area_name: string
  description?: string
  sort?: number
  active?: number
}

export function useTablesExcelParser() {
  const parseExcelFile = async (file: File): Promise<ParsedTableData[]> => {
    try {
      // Dynamic import for xlsx
      const XLSX = await import('xlsx')

      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      // Parse data according to format: Row 1: headers, Row 2+: data
      const parsedTables: ParsedTableData[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]

        // Skip empty rows
        if (!row || row.length === 0 || !row[0]) continue

        const area: ParsedTableData = {
          area_name: String(row[0] || '').trim(), // Column 1: name
          description: row[1] ? String(row[1]).trim() : undefined, // Column 2: description
          sort: undefined, // Not used in new format
          active: 1 // Default to active
        }

        // Validate required fields
        if (!area.area_name) {
          toast.error(`Dòng ${i + 1}: Tên bàn không được để trống`)
          continue
        }

        parsedTables.push(area)
      }

      if (parsedTables.length === 0) {
        toast.error('Không tìm thấy dữ liệu hợp lệ trong file')
        throw new Error('No valid data found')
      }

      return parsedTables
    } catch (error) {
      console.error('Error parsing Excel file:', error)
      if (error instanceof Error && error.message !== 'No valid data found') {
        toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      }
      throw error
    }
  }

  const downloadTemplate = () => {
    // Create template Excel file
    const templateData = [
      ['name', 'description'],
      ['Test 1', 'Khu vực tầng 2'],
      ['Test 2', 'Khu vực tầng 3']
    ]

    // Dynamic import for xlsx
    import('xlsx').then(XLSX => {
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(templateData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Tables Template')
      XLSX.writeFile(workbook, 'import_area_template.xlsx')
      toast.success('Đã tải file mẫu thành công!')
    })
  }

  return {
    parseExcelFile,
    downloadTemplate
  }
}
