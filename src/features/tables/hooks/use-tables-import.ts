import { useState, useRef } from 'react'
import { toast } from 'sonner'

import { useTablesExcelParser } from './use-tables-excel-parser'
import { useBulkImportTables } from '@/hooks/api/use-tables'

interface ParsedTableData {
  area_name: string
  description?: string
  sort?: number
  active?: number
}

export function useTablesImport() {
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedTableData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  const { parseExcelFile, downloadTemplate } = useTablesExcelParser()
  const bulkImportTablesMutation = useBulkImportTables()

  const resetImportState = () => {
    setShowImportParsedData(false)
    setImportParsedData([])
    setImportSelectedFile(null)
  }

  const handleDownloadTemplate = () => {
    downloadTemplate()
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedTables = await parseExcelFile(file)
      setImportParsedData(parsedTables)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedTables.length} bàn từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedTables = async (storeUid: string) => {
    if (importParsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      await bulkImportTablesMutation.mutateAsync({
        storeUid,
        tables: importParsedData
      })
      toast.success(`Đã tạo thành công ${importParsedData.length} bàn!`)
      resetImportState()
      return true
    } catch {
      toast.error('Lỗi khi tạo bàn. Vui lòng thử lại.')
      return false
    }
  }

  return {
    importSelectedFile,
    importParsedData,
    showImportParsedData,
    importFileInputRef,
    resetImportState,
    handleDownloadTemplate,
    handleImportFileUpload,
    handleImportFileChange,
    handleSaveImportedTables,
    isLoading: bulkImportTablesMutation.isPending,
  }
}
