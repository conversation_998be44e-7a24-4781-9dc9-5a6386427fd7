import { useState, useMemo, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { Plus, Upload, Settings, Edit, ArrowUpDown, Wrench } from 'lucide-react'

import { useAreasData, useTablesData } from '@/hooks/api'

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui'

import { TablesDataTable, createTablesColumns, ImportTablesModal } from './'

interface Store {
  id: string
  store_name: string
  active: number
}

export function TablesList() {
  const navigate = useNavigate()

  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      console.error('Error parsing pos_stores_data:', error)
      return []
    }
  }, [])

  const [selectedStoreId, setSelectedStoreId] = useState<string>(stores[0]?.id || '')
  const [selectedAreaId, setSelectedAreaId] = useState<string>('all')
  const [isImportModalOpen, setIsImportModalOpen] = useState(false)

  // Reset area selection when no store is selected
  useEffect(() => {
    if (!selectedStoreId) {
      setSelectedAreaId('all')
    }
  }, [selectedStoreId])

  // Fetch areas data based on selected store - only when store is selected
  const { data: areas = [], isLoading: isLoadingAreas } = useAreasData({
    storeUid: selectedStoreId,
    page: 1,
    results_per_page: 15000
  })

  const {
    data: tables = [],
    isLoading,
    error
  } = useTablesData({
    storeUid: selectedStoreId
  })

  const columns = useMemo(() => createTablesColumns(selectedStoreId), [selectedStoreId])

  // Filter tables based on selected area
  const filteredTables = useMemo(() => {
    if (selectedAreaId === 'all') {
      return tables
    }
    // Filter tables that belong to the selected area
    // Match table's area_uid with selected area id
    return tables.filter(table => table.area_uid === selectedAreaId)
  }, [tables, selectedAreaId])

  const handleCreateNew = () => {
    navigate({ to: '/setting/table/detail' })
  }

  const handleImportFromFile = () => {
    if (!selectedStoreId) {
      return
    }
    setIsImportModalOpen(true)
  }

  const handleCloseImportModal = () => {
    setIsImportModalOpen(false)
  }

  const handleImportSuccess = () => {
    setIsImportModalOpen(false)
  }

  const handleEditTableInfo = () => {
    console.log('Sửa thông tin bàn')
    // TODO: Implement edit table info functionality
  }

  const handleArrangeTable = () => {
    console.log('Sắp xếp bàn')
    // TODO: Implement arrange table functionality
  }

  const handleConfigureTable = () => {
    console.log('Cấu hình bàn')
    // TODO: Implement configure table functionality
  }

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('all') // Reset area selection when store changes
  }

  return (
    <div className='container mx-auto space-y-6 py-6'>
      <div className='flex items-center gap-4'>
        <h1 className='text-3xl font-bold tracking-tight'>Danh sách bàn</h1>

        <div className='flex flex-1 gap-4'>
          <Select value={selectedStoreId} onValueChange={handleStoreChange}>
            <SelectTrigger className='w-[300px]'>
              <SelectValue placeholder='Chọn cửa hàng' />
            </SelectTrigger>
            <SelectContent>
              {stores.map(store => (
                <SelectItem key={store.id} value={store.id}>
                  {store.store_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={selectedAreaId}
            onValueChange={setSelectedAreaId}
            disabled={!selectedStoreId || isLoadingAreas}
          >
            <SelectTrigger className='w-[300px]'>
              <SelectValue
                placeholder={
                  !selectedStoreId
                    ? 'Chọn cửa hàng trước'
                    : isLoadingAreas
                      ? 'Đang tải...'
                      : 'Chọn khu vực'
                }
              />
            </SelectTrigger>
            <SelectContent>
              {selectedStoreId && (
                <>
                  <SelectItem value='all'>Tất cả khu vực</SelectItem>
                  {areas.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.area_name}
                    </SelectItem>
                  ))}
                </>
              )}
            </SelectContent>
          </Select>
        </div>

        <div className='flex gap-2'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline'>
                <Wrench className='mr-2 h-4 w-4' />
                Tiện ích
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end' className='w-56'>
              <DropdownMenuItem onClick={handleImportFromFile}>
                <Upload className='mr-2 h-4 w-4' />
                Thêm bàn từ file
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEditTableInfo}>
                <Edit className='mr-2 h-4 w-4' />
                Sửa thông tin bàn
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleArrangeTable}>
                <ArrowUpDown className='mr-2 h-4 w-4' />
                Sắp xếp bàn
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleConfigureTable}>
                <Settings className='mr-2 h-4 w-4' />
                Cấu hình bàn
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={handleCreateNew}>
            <Plus className='mr-2 h-4 w-4' />
            Tạo bàn mới
          </Button>
        </div>
      </div>

      {/* Tables Table */}
      {isLoading && (
        <div className='rounded-md border p-8 text-center'>
          <p className='text-muted-foreground'>Đang tải...</p>
        </div>
      )}
      {error && (
        <div className='rounded-md border p-8 text-center'>
          <p className='text-muted-foreground'>Có lỗi xảy ra khi tải dữ liệu</p>
        </div>
      )}
      {!isLoading && !error && (
        <TablesDataTable columns={columns} data={filteredTables} storeUid={selectedStoreId} />
      )}

      {!selectedStoreId && stores.length > 0 && (
        <div className='bg-card rounded-lg border p-8 text-center'>
          <p className='text-muted-foreground'>Vui lòng chọn cửa hàng để xem danh sách bàn</p>
        </div>
      )}

      {stores.length === 0 && (
        <div className='bg-card rounded-lg border p-8 text-center'>
          <p className='text-muted-foreground'>Không có cửa hàng nào khả dụng</p>
        </div>
      )}

      {/* Import Tables Modal */}
      <ImportTablesModal
        open={isImportModalOpen}
        onOpenChange={setIsImportModalOpen}
        onCancel={handleCloseImportModal}
        onSuccess={handleImportSuccess}
      />
    </div>
  )
}
