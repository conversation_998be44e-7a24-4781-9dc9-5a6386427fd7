import { createFileRoute } from '@tanstack/react-router'

import { CreateTableForm } from '@/features/tables'

interface TableDetailSearch {
  store_uid: string
}

export const Route = createFileRoute('/_authenticated/setting/table/detail/$areaId')({
  component: TableDetailPage,
  validateSearch: (search: Record<string, unknown>): TableDetailSearch => ({
    store_uid: search.store_uid as string
  })
})

function TableDetailPage() {
  const { areaId } = Route.useParams()
  const { store_uid } = Route.useSearch()

  console.log('🔍 TableDetailPage rendered')
  console.log('📍 Route params:', { areaId })
  console.log('🔍 Route search:', { store_uid })
  console.log('📊 Full URL:', window.location.href)

  return <CreateTableForm areaId={areaId} storeUid={store_uid} />
}
