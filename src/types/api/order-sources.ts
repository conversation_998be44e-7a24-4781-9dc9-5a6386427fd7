interface OrderSourceBase {
  id: string
  source_id: string
  source_name: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
  deleted: boolean
  deleted_at: number | null
  deleted_by: string | null
  source_type: string[]
  description: string | null
  extra_data: Record<string, unknown> | null
  is_fb: number
  active: number
  revision: number | null
  brand_uid: string
  company_uid: string
  sort: number
  is_fabi: number
  store_uid: string | null
  partner_config: number
  row: number
}

export interface OrderSource extends OrderSourceBase {
  stores: number | null
}

export interface OrderSourceDetail extends OrderSourceBase {
  stores: Array<{ id: string; store_name: string; [key: string]: unknown }> | null
}

export interface OrderSourcesResponse {
  data: OrderSource[]
  track_id: string
}

export interface CreateOrderSourceRequest {
  source_name: string
  source_id?: string
  stores: string[]
  description?: string | null
  active?: number
  sort?: number
  source_type?: string[]
  extra_data?: Record<string, unknown>
  is_fabi?: number
  partner_config?: number
}

export interface UpdateOrderSourceRequest extends Partial<CreateOrderSourceRequest> {
  id: string
}

export interface DeleteOrderSourceRequest {
  id: string
  company_uid: string
  brand_uid: string
}
