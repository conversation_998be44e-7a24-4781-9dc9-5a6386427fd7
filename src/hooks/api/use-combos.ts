import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import {
  combosApi,
  type CombosListParams,
  type ComboPromotionsListParams,
  type DeleteCombosParams
} from '@/lib/combos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export interface UseCombosDataOptions {
  storeUid?: string
  page?: number
  status?: 'unexpired' | 'expired' | 'all'
  search?: string
  promotionUid?: string
  enabled?: boolean
}

/**
 * Hook to fetch combos data
 */
export const useCombosData = (options: UseCombosDataOptions = {}) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { storeUid, page = 1, status = 'all', search, promotionUid, enabled = true } = options

  return useQuery({
    queryKey: [
      QUERY_KEYS.COMBOS_LIST,
      {
        company_uid: company?.id,
        brand_uid: selectedBrand?.id,
        storeUid,
        page,
        status,
        search,
        promotionUid
      }
    ],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Company or brand not found')
      }

      const params: CombosListParams = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        page,
        status
      }

      if (storeUid) {
        params.list_store_uid = storeUid
      }

      if (search) {
        params.search = search
      }

      if (promotionUid) {
        params.promotion_uid = promotionUid
      }

      const response = await combosApi.getCombosList(params)
      return response.data
    },
    enabled: enabled && !!company?.id && !!selectedBrand?.id
  })
}

/**
 * Hook to fetch combo promotions data
 */
export const useComboPromotionsData = (options: { enabled?: boolean; storeUid?: string } = {}) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { enabled = true, storeUid } = options

  return useQuery({
    queryKey: [
      QUERY_KEYS.COMBO_PROMOTIONS_LIST,
      {
        company_uid: company?.id,
        brand_uid: selectedBrand?.id,
        storeUid
      }
    ],
    queryFn: async () => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Company or brand not found')
      }

      const params: ComboPromotionsListParams = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        skip_limit: true,
        aggregate: true
      }

      if (storeUid) {
        params.store_uid = storeUid
      }

      const response = await combosApi.getComboPromotionsList(params)
      return response.data
    },
    enabled: enabled && !!company?.id && !!selectedBrand?.id
  })
}

/**
 * Hook to delete combos
 */
export const useDeleteCombos = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: DeleteCombosParams) => combosApi.deleteCombos(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.COMBOS_LIST]
      })
      toast.success('Xóa combo thành công')
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa combo'
      toast.error(errorMessage)
    }
  })
}
