import * as React from 'react'

import { ChevronsUpDown, Plus } from 'lucide-react'

import { useCurrentBrand, type BrandDisplay } from '@/stores/posStore'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from '@/components/ui/sidebar'

import { useBrandsData } from './data/user-teams'

export function BrandSwitcher() {
  const { isMobile } = useSidebar()
  const brands = useBrandsData()
  const { selectedBrand, setSelectedBrand } = useCurrentBrand()
  const [isLoading, setIsLoading] = React.useState(false)

  // Auto-select first brand if none is selected
  React.useEffect(() => {
    if (!selectedBrand && brands.length > 0) {
      setSelectedBrand(brands[0])
    }
  }, [selectedBrand, brands, setSelectedBrand])

  if (!selectedBrand) {
    // Show loading state while waiting for brand selection
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size='lg' disabled>
            <div className='bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg'>
              <div className='size-4 animate-pulse rounded bg-current' />
            </div>
            <div className='grid flex-1 text-left text-sm leading-tight'>
              <span className='truncate font-semibold'>Loading...</span>
              <span className='truncate text-xs'>Selecting brand</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  const handleBrandSwitch = (brand: BrandDisplay) => {
    setIsLoading(true)
    setSelectedBrand(brand)

    // Short loading delay for visual feedback
    setTimeout(() => {
      setIsLoading(false)
    }, 200)
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size='lg'
              className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
              disabled={isLoading}
            >
              <div className='bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg'>
                {selectedBrand.logo && typeof selectedBrand.logo === 'function' ? (
                  React.createElement(selectedBrand.logo, { className: 'size-4' })
                ) : (
                  <div className='size-4 rounded bg-current' />
                )}
              </div>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>
                  {isLoading ? 'Switching...' : selectedBrand.name}
                </span>
                <span className='truncate text-xs'>{selectedBrand.plan}</span>
              </div>
              <ChevronsUpDown className='ml-auto' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg'
            align='start'
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className='text-muted-foreground text-xs'>Brands</DropdownMenuLabel>
            {brands.map((brand, index) => (
              <DropdownMenuItem
                key={brand.id || brand.name}
                onClick={() => handleBrandSwitch(brand)}
                className='gap-2 p-2'
                disabled={isLoading}
              >
                <div className='flex size-6 items-center justify-center rounded-sm border'>
                  {brand.logo && typeof brand.logo === 'function' ? (
                    React.createElement(brand.logo, { className: 'size-4 shrink-0' })
                  ) : (
                    <div className='size-4 rounded bg-current' />
                  )}
                </div>
                <div className='flex flex-col'>
                  <span className='font-medium'>{brand.name}</span>
                  {brand.currency && (
                    <span className='text-muted-foreground text-xs'>
                      {brand.currency} • {brand.brandId || 'ID'}
                    </span>
                  )}
                </div>
                <div className='ml-auto flex items-center gap-2'>
                  {selectedBrand?.id === brand.id && (
                    <div className='bg-primary size-2 rounded-full' />
                  )}
                  <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                </div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className='gap-2 p-2'>
              <div className='bg-background flex size-6 items-center justify-center rounded-md border'>
                <Plus className='size-4' />
              </div>
              <div className='text-muted-foreground font-medium'>Add brand</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
