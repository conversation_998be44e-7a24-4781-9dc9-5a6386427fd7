import { IconMessages, IconUsers, IconDevices, IconReceiptDollar } from '@tabler/icons-react'

import { type NavItem } from '../types'

export const businessNavItems: NavItem[] = [
  {
    title: 'Chương Trình',
    icon: IconReceiptDollar,
    items: [
      {
        title: '<PERSON><PERSON><PERSON>ến mãi',
        url: '/sale/promotion'
      },
      {
        title: 'Combo',
        url: '/sale/combo'
      },
      {
        title: 'Gi<PERSON>m giá',
        url: '/sale-channel/discount'
      },
      {
        title: 'Gi<PERSON>m giá theo hội viên',
        url: '/sale/member-discount',
        disabled: true
      },
      {
        title: 'Chiết khấu thanh toán',
        url: '/sale/payment-discount',
        disabled: true
      },
      {
        title: 'Phí dịch vụ',
        url: '/sale/service-fee',
        disabled: true
      }
    ]
  },
  {
    title: '<PERSON><PERSON><PERSON> Bán <PERSON>àng',
    icon: IconMessages,
    items: [
      {
        title: '<PERSON><PERSON><PERSON> bán hàng',
        url: '/sale-channel/channel'
      }
    ]
  },
  {
    title: 'T<PERSON><PERSON><PERSON> Bị',
    icon: IconDevices,
    items: [
      {
        title: '<PERSON>u<PERSON>n lý thiết bị',
        url: '/devices/list'
      },
      {
        title: 'Loại thiết bị',
        url: '/devices/types'
      }
    ]
  },
  {
    title: 'Nhân Viên',
    icon: IconUsers,
    items: [
      {
        title: 'Danh sách nhân viên',
        url: '/employee/list'
      },
      {
        title: 'Danh sách chức vụ',
        url: '/employee/role'
      }
    ]
  },
  {
    title: 'Ứng Dụng',
    url: '/apps/store',
    badge: '3',
    icon: IconMessages
  }
]
